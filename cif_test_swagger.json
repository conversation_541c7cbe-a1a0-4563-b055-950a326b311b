{"swagger": "2.0", "info": {"title": "API Documentation", "description": "Generated from Excel specification", "version": "1.0.0"}, "host": "localhost:8080", "basePath": "/", "schemes": ["http", "https"], "paths": {"/cif/file/batch/result/export": {"post": {"tags": null, "summary": "批量开立结果文件生成", "description": "生成批量开立结果文件", "operationId": "ICore12209117", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"batchNo": {"type": "string", "description": "批次号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"fileName": {"type": "string", "description": "文件名称"}}}}}}}, "/cif/file/client/batchcreate": {"post": {"tags": null, "summary": "批量开立客户信息", "description": "以Excel文件的形式批量导入客户基本信息，并批量开立客户信息，返回结果文件路径与文件名", "operationId": "ICore12209001", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientCategoryType": {"type": "string", "description": "客户分类"}, "clientType": {"type": "string", "description": "客户类型"}, "contactType": {"type": "string", "description": "联系类型"}, "fileName": {"type": "string", "description": "文件名称"}, "filePath": {"type": "string", "description": "文件路径"}, "openBranch": {"type": "string", "description": "开立机构"}, "openDate": {"type": "string", "description": "开立日期"}, "openType": {"type": "string", "description": "批量开立方式"}, "totalNum": {"type": "string", "description": "总数量"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"contrastBatNo": {"type": "string", "description": "他行批次号"}}}}}}}, "/cif/file/client/batchquery": {"post": {"tags": null, "summary": "批量查询客户和账户信息", "description": "批量查询客户和账户信息", "operationId": "ICore12209111", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"fileName": {"type": "string", "description": "文件名称"}, "filePath": {"type": "string", "description": "文件路径"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"fileName": {"type": "string", "description": "文件名称"}, "filePath": {"type": "string", "description": "文件路径"}}}}}}}, "/cif/file/client/clientInfoSynchron": {"post": {"tags": null, "summary": "客户信息同步", "description": "客户信息同步（文件）", "operationId": "ICore12209002", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"filePath": {"type": "string", "description": "文件路径"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/file/verify/batchregister": {"post": {"tags": null, "summary": "批量核查结果登记", "description": "以txt文件的形式批量导入客户核查结果，记录个人客户身份核实情况表", "operationId": "ICore12209118", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"fileName": {"type": "string", "description": "文件名称"}, "filePath": {"type": "string", "description": "文件路径"}, "openBranch": {"type": "string", "description": "开立机构"}, "openDate": {"type": "string", "description": "开立日期"}, "openType": {"type": "string", "description": "批量开立方式"}, "totalNum": {"type": "string", "description": "总数量"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"contrastBatNo": {"type": "string", "description": "他行批次号"}}}}}}}, "/cif/file/verify/downloadresfile": {"post": {"tags": null, "summary": "批量核查结果文件下载", "description": "根据上送的批次号，在个人客户身份核实情况表中查询信息，写入txt文件，返回文件路径文件名", "operationId": "ICore12209119", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"contrastBatNo": {"type": "string", "description": "他行批次号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"resultFileName": {"type": "string", "description": "结果文件名"}}}}}}}, "/cif/inq/branch/change/checkinfo": {"post": {"tags": null, "summary": "机构撤并客户校验项查询", "description": "机构撤并前检查，检查拆出机构不能有未完成的客户合并", "operationId": "ICore14009120", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"newBranch": {"type": "string", "description": "变更后机构"}, "oldBranch": {"type": "string", "description": "变更前机构"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/channel/control": {"post": {"tags": null, "summary": "客户渠道限制查询", "description": "客户渠道限制查询", "operationId": "ICore14009165", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "controlStatus": {"type": "string", "description": "控制状态"}, "controlType": {"type": "string", "description": "控制类型"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/acctexec": {"post": {"tags": null, "summary": "客户经理信息查询", "description": "根据客户经理编号，查询客户经理的详细信息列表", "operationId": "ICore14009111", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"acctExec": {"type": "string", "description": "客户经理"}, "acctExecName": {"type": "string", "description": "客户经理姓名"}, "branch": {"type": "string", "description": "所属机构号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/amend": {"post": {"tags": null, "summary": "业务信息修改记录查询", "description": "查询客户信息的维护前后变更信息", "operationId": "ICore14009112", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"amendDate": {"type": "string", "description": "变更日期"}, "clientNo": {"type": "string", "description": "客户号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/batchcreate": {"post": {"tags": null, "summary": "客户批量开立查询", "description": "查询批量开立客户信息，并支持结果文件下载，可供查询的类型有A-全部查询、F-失败记录查询、S-成功记录查询", "operationId": "ICore14009118", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"batchNo": {"type": "string", "description": "批次号"}, "queryFlag": {"type": "string", "description": "查询标志"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/block": {"post": {"tags": null, "summary": "根据客户号查询客户冻结信息", "description": "根据客户号或证件号查询客户冻结信息", "operationId": "ICore14009116", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "issCountry": {"type": "string", "description": "发证国家"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/byacctexec": {"post": {"tags": null, "summary": "按客户经理查询客户信息", "description": "按客户经理查询客户号名称证件等简要信息", "operationId": "ICore14009109", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"acctExec": {"type": "string", "description": "客户经理"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/contact": {"post": {"tags": null, "summary": "根据客户号查询客户联系人信息", "description": "根据客户号查询客户联系人信息", "operationId": "ICore14009182", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/detail": {"post": {"tags": null, "summary": "客户详细信息查询", "description": "根据客户号或者证件信息查询客户详细信息，返回客户相关的所有信息", "operationId": "ICore14009105", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "company": {"type": "string", "description": "法人"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "issCountry": {"type": "string", "description": "发证国家"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/document": {"post": {"tags": null, "summary": "客户证件信息查询", "description": "根据证件信息查询客户简要信息和证件信息，支持使用一二代身份证或户口本查询", "operationId": "ICore14000006", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientShort": {"type": "string", "description": "客户简称"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "issCountry": {"type": "string", "description": "发证国家"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/document/batch": {"post": {"tags": null, "summary": "客户证件批量查询", "description": "根据证件类型证件号码批量查询客户证件信息", "operationId": "ICore14009113", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}}}}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/document/expir": {"post": {"tags": null, "summary": "客户证件到期信息查询", "description": "根据客户类型，证件类型、起始日期、结束日期进行客户证件到期信息查询", "operationId": "ICore14000017", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"branch": {"type": "string", "description": "所属机构号"}, "documentType": {"type": "string", "description": "证件类型"}, "endDate": {"type": "string", "description": "结束日期"}, "individualFlag": {"type": "string", "description": "对公对私标志"}, "startDate": {"type": "string", "description": "开始日期"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/fatca": {"post": {"tags": null, "summary": "FATCA信息查询", "description": "根据客户号查询客户FATCA信息", "operationId": "ICore14009981", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/group": {"post": {"tags": null, "summary": "客户组成员信息查询", "description": "查询上送客户号所在客户组的所有客户信息数组", "operationId": "ICore14009108", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/info": {"post": {"tags": null, "summary": "客户首选信息查询", "description": "根据客户号或证件信息查询单笔客户详细信息，并返回客户首选证件信息，首选联系信息，客户冻结信息数组；此接口比1400-9100返回信息稍微少点，主要用于核心系统内部调用", "operationId": "ICore14009101", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "company": {"type": "string", "description": "法人"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "internalFlag": {"type": "string", "description": "行内交易标识"}, "issCountry": {"type": "string", "description": "发证国家"}, "resFlag": {"type": "string", "description": "冻结标志"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/isindividual": {"post": {"tags": null, "summary": "根据客户类型查询对公对私标志", "description": "根据客户类型，查询参数配置，返回参数配置的客户类型对应的对公对私标志", "operationId": "ICore14007925", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientType": {"type": "string", "description": "客户类型"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"clientType": {"type": "string", "description": "客户类型"}, "clientTypeDesc": {"type": "string", "description": "客户类型描述"}, "company": {"type": "string", "description": "法人"}, "isIndividual": {"type": "string", "description": "个体客户标志"}}}}}}}, "/cif/inq/client/joint": {"post": {"tags": null, "summary": "联名客户查询", "description": "联名客户查询", "operationId": "ICore14009115", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "jointNo": {"type": "string", "description": "联名编号"}, "virtualClientNo": {"type": "string", "description": "虚拟客户号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/mergeinfo": {"post": {"tags": null, "summary": "客户合并查询", "description": "查询客户合并登记簿信息", "operationId": "ICore14009121", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"mergeNo": {"type": "string", "description": "合并编号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/multi": {"post": {"tags": null, "summary": "查询客户基本信息(多笔)", "description": "根据条件查询，返回多笔客户详细信息，并返回证件信息和联系信息数组", "operationId": "ICore14009102", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"acctName": {"type": "string", "description": "账户名称"}, "baseAcctNo": {"type": "string", "description": "账号/卡号"}, "categoryType": {"type": "string", "description": "客户细分类型"}, "chClientName": {"type": "string", "description": "客户中文名称"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientType": {"type": "string", "description": "客户类型"}, "contactTel": {"type": "string", "description": "联系电话  "}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "fuzzyQueryFlag": {"type": "string", "description": "客户名模糊查询标志"}, "issCountry": {"type": "string", "description": "发证国家"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/phone/repeat/check": {"post": {"tags": null, "summary": "查询和校验个人客户手机号码重复", "description": "查询个人客户重复手机号码的客户信息列表，也可以直接抛出手机号码重复的错误代码，目前标准版本暂未使用", "operationId": "ICore14009117", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "contactTel": {"type": "string", "description": "联系电话  "}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "issCountry": {"type": "string", "description": "发证国家"}, "phoneRepeatOption": {"type": "string", "description": "客户手机号码重复校验操作标志"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/relation": {"post": {"tags": null, "summary": "客户关系查询", "description": "根据客户号，查询该客户之前在系统中维护的客户关系信息", "operationId": "ICore14009104", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/res": {"post": {"tags": null, "summary": "CRS信息查询", "description": "根据客户号查询客户res信息", "operationId": "ICore14009971", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"annualThreshold": {"type": "string", "description": "年度重分类阈值"}, "area": {"type": "string", "description": "国家地区"}, "balanceThreshold": {"type": "string", "description": "补救平衡阈值"}, "ccy": {"type": "string", "description": "币种"}, "city": {"type": "string", "description": "城市"}, "clientNo": {"type": "string", "description": "客户号"}, "einNo": {"type": "string", "description": "系统编号"}, "homeAddr": {"type": "string", "description": "居住地址"}, "jsonBranch": {"type": "string", "description": "机构信息大字段"}, "jsonControl": {"type": "string", "description": "实际控制人信息大字段"}, "jsonPeople": {"type": "string", "description": "个人信息大字段"}, "province": {"type": "string", "description": "省"}, "taxCountry": {"type": "string", "description": "税收居民国"}, "taxGovernment": {"type": "string", "description": "税务分类制度"}, "taxResidentFlag": {"type": "string", "description": "税收居民标识"}, "taxStatus": {"type": "string", "description": "税务分类状况"}}}}}}}, "/cif/inq/client/resemble": {"post": {"tags": null, "summary": "相似客户查询", "description": "根据相似规则，查询规则匹配的所有相似的客户信息列表", "operationId": "ICore14009107", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"analogueGroup": {"type": "string", "description": "相似组"}, "analogueRuleId": {"type": "string", "description": "相似规则编号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/restraint": {"post": {"tags": null, "summary": "根据客户号查询客户级限制信息", "description": "根据客户号查询客户级限制信息，以数组的形式返回客户所有限制信息", "operationId": "ICore14000148", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"branch": {"type": "string", "description": "所属机构号"}, "clientNo": {"type": "string", "description": "客户号"}, "endDate": {"type": "string", "description": "结束日期"}, "showFlag": {"type": "string", "description": "是否查询解限信息"}, "startDate": {"type": "string", "description": "开始日期"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/similarity/query": {"post": {"tags": null, "summary": "客户相似信息查询", "description": "通过客户相关信息查询相似信息", "operationId": "ICore14009114", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "phone": {"type": "string", "description": "手机号"}, "recognizeRule": {"type": "string", "description": "客户相似度识别规则"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/client/single": {"post": {"tags": null, "summary": "客户基本信息查询", "description": "根据客户号或证件信息查询单笔客户详细信息，并返回客户证件信息，联系信息，联系人信息数组", "operationId": "ICore14009100", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "internalFlag": {"type": "string", "description": "行内交易标识"}, "issCountry": {"type": "string", "description": "发证国家"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/list/importInfo": {"post": {"tags": null, "summary": "批量导入查询", "description": "查询批量导入的人行原始黑灰名单表信息", "operationId": "ICore14007003", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"endDate": {"type": "string", "description": "结束日期"}, "startDate": {"type": "string", "description": "开始日期"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/param/queryparam": {"post": {"tags": null, "summary": "参数统一查询", "description": "参数统一查询接口，用于后管系统查询下拉列表配置的参数", "operationId": "ICore14009130", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"paramMap": {"type": "string", "description": "参数集合"}, "sqlId": {"type": "string", "description": "sql编号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/inq/user/access": {"post": {"tags": null, "summary": "柜员权限查询", "description": "柜员权限查询", "operationId": "ICore14003002", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"userId": {"type": "string", "description": "交易柜员"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"businessList": {"type": "string", "description": "行业代码组"}, "clientTypeList": {"type": "string", "description": "客户类型组"}, "occupationCodeList": {"type": "string", "description": "职业组"}, "userId": {"type": "string", "description": "交易柜员"}}}}}}}, "/cif/inq/user/defined": {"post": {"tags": null, "summary": "查询用户自定义参数", "description": "查询用户自定义数据表", "operationId": "ICore14009997", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"applicationFlag": {"type": "string", "description": "应用标志"}, "columnCode": {"type": "string", "description": "字段编码"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}, "/cif/nfin/channel/control": {"post": {"tags": null, "summary": "客户渠道限制", "description": "对客户进行限制维护,包含新增或解除限制,或者修改限制", "operationId": "ICore12009165", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "controlSeqNo": {"type": "string", "description": "控制编号"}, "controlType": {"type": "string", "description": "控制类型"}, "documentId": {"type": "string", "description": "证件号码"}, "endDate": {"type": "string", "description": "结束日期"}, "limitLevel": {"type": "string", "description": "限制级别"}, "narrative": {"type": "string", "description": "摘要"}, "operateFlag": {"type": "string", "description": "操作类型"}, "startDate": {"type": "string", "description": "开始日期"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"controlSeqNo": {"type": "string", "description": "控制编号"}}}}}}}, "/cif/nfin/client/acctexec/maint": {"post": {"tags": null, "summary": "客户经理维护", "description": "对客户经理信息进行新增修改删除", "operationId": "ICore12009110", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"acctExec": {"type": "string", "description": "客户经理"}, "acctExecName": {"type": "string", "description": "客户经理姓名"}, "acctExecType": {"type": "string", "description": "客户经理类型"}, "branch": {"type": "string", "description": "所属机构号"}, "collatMgrInd": {"type": "string", "description": "是否担保经理"}, "contactTel": {"type": "string", "description": "联系电话  "}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "issCountry": {"type": "string", "description": "发证国家"}, "manager": {"type": "string", "description": "主管经理"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "profitCenter": {"type": "string", "description": "利润中心 "}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"acctExec": {"type": "string", "description": "客户经理"}}}}}}}, "/cif/nfin/client/block/maint": {"post": {"tags": null, "summary": "客户冻结维护", "description": "对客户进行冻结，支持冻结或解冻", "operationId": "ICore12009116", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"blockDate": {"type": "string", "description": "冻结日期"}, "blockReason": {"type": "string", "description": "冻结原因"}, "clientNo": {"type": "string", "description": "客户号"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/cancel": {"post": {"tags": null, "summary": "客户信息注销与激活", "description": "客户信息注销，注销后激活", "operationId": "ICore12009120", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"changeReason": {"type": "string", "description": "变更原因"}, "clientNo": {"type": "string", "description": "客户号"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/change": {"post": {"tags": null, "summary": "客户变更", "description": "客户机构变更", "operationId": "ICore12009174", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "newBranch": {"type": "string", "description": "变更后机构"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/create": {"post": {"tags": null, "summary": "快速建立客户信息", "description": "使用客户信息中少量关键要素，快速建立客户信息，用最少的客户信息创建客户，以便尽快可以使用新客户号去开立账户等进行后续交易", "operationId": "ICore12009100", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"acctExec": {"type": "string", "description": "客户经理"}, "address": {"type": "string", "description": "地址"}, "birthDate": {"type": "string", "description": "出生日期"}, "categoryType": {"type": "string", "description": "客户细分类型"}, "clientCity": {"type": "string", "description": "城市代码"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientPoint": {"type": "string", "description": "客户积分"}, "clientShort": {"type": "string", "description": "客户简称"}, "clientType": {"type": "string", "description": "客户类型"}, "company": {"type": "string", "description": "法人"}, "contactInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"address": {"type": "string", "description": "地址"}, "address1": {"type": "string", "description": "地址1"}, "city": {"type": "string", "description": "城市"}, "cityTel": {"type": "string", "description": "电话区号"}, "contactTel": {"type": "string", "description": "联系电话  "}, "contactType": {"type": "string", "description": "联系类型"}, "country": {"type": "string", "description": "国家"}, "countryTel": {"type": "string", "description": "国家电话区号"}, "postalCode": {"type": "string", "description": "邮政编码"}, "prefFlag": {"type": "string", "description": "首选标志"}, "route": {"type": "string", "description": "联系方式类型"}, "state": {"type": "string", "description": "省别代码"}}}}, "contributeDegree": {"type": "string", "description": "贡献度"}, "countryLoc": {"type": "string", "description": "国籍"}, "ctrlBranch": {"type": "string", "description": "控制分行"}, "documentInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "issCountry": {"type": "string", "description": "发证国家"}, "issDate": {"type": "string", "description": "签发日期"}, "issPlace": {"type": "string", "description": "签发地"}, "issueBranch": {"type": "string", "description": "签发机构"}, "maturityDate": {"type": "string", "description": "到期日期"}, "prefFlag": {"type": "string", "description": "首选标志"}, "visaType": {"type": "string", "description": "签证类型"}}}}, "enClientName": {"type": "string", "description": "客户英文名称"}, "inlandOffshore": {"type": "string", "description": "境内境外标志"}, "maritalStatus": {"type": "string", "description": "婚姻状况"}, "sex": {"type": "string", "description": "性别"}, "state": {"type": "string", "description": "省别代码"}, "taxResidentFlag": {"type": "string", "description": "税收居民标识"}, "unverificationReason": {"type": "string", "description": "无法核实原因"}, "verificationResult": {"type": "string", "description": "核查结果"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}}}}}, "/cif/nfin/client/crs/maint": {"post": {"tags": null, "summary": "CRS信息维护", "description": "根据上送信息维护CRS信息", "operationId": "ICore12009971", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"annualThreshold": {"type": "string", "description": "年度重分类阈值"}, "area": {"type": "string", "description": "国家地区"}, "balanceThreshold": {"type": "string", "description": "补救平衡阈值"}, "ccy": {"type": "string", "description": "币种"}, "city": {"type": "string", "description": "城市"}, "clientNo": {"type": "string", "description": "客户号"}, "einNo": {"type": "string", "description": "系统编号"}, "homeAddr": {"type": "string", "description": "居住地址"}, "jsonBranch": {"type": "string", "description": "机构信息大字段"}, "jsonControl": {"type": "string", "description": "实际控制人信息大字段"}, "jsonPeople": {"type": "string", "description": "个人信息大字段"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "province": {"type": "string", "description": "省"}, "taxCountry": {"type": "string", "description": "税收居民国"}, "taxGovernment": {"type": "string", "description": "税务分类制度"}, "taxResidentFlag": {"type": "string", "description": "税收居民标识"}, "taxStatus": {"type": "string", "description": "税务分类状况"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/fatca/maint": {"post": {"tags": null, "summary": "FATCA信息维护", "description": "根据上送信息FATCA信息维护", "operationId": "ICore12009981", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "facilityJson": {"type": "string", "description": "海外账户税收合规法案信息大字段"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "residentArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"noTaxpayer": {"type": "string", "description": "无纳税人识别号"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "remark1": {"type": "string", "description": "备注1"}, "remark2": {"type": "string", "description": "备注2"}, "taxpayerId": {"type": "string", "description": "纳税人识别号"}}}}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/joint/maint": {"post": {"tags": null, "summary": "联名客户关系建立", "description": "联名客户关系建立。自动生成联名客户号（相当于虚拟客户），并建立联名关系。", "operationId": "ICore12009112", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"array": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "mainClientFlag": {"type": "string", "description": "主客户标识"}, "option": {"type": "string", "description": "操作类型"}}}}, "jointNo": {"type": "string", "description": "联名编号"}, "jointType": {"type": "string", "description": "联名类型"}, "option": {"type": "string", "description": "操作类型"}, "virtualClientNo": {"type": "string", "description": "虚拟客户号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "jointNo": {"type": "string", "description": "联名编号"}, "virtualClientNo": {"type": "string", "description": "虚拟客户号"}}}}}}}, "/cif/nfin/client/merge": {"post": {"tags": null, "summary": "客户合并", "description": "将被合并客户废弃，保留新客户信息，并广播通知其他系统更新登记簿的客户号；此接口可以进行合并处理，也可进行合并前的检查", "operationId": "ICore12009121", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientMergeOption": {"type": "string", "description": "客户合并操作类型"}, "mergeNo": {"type": "string", "description": "合并编号"}, "newClientName": {"type": "string", "description": "新客户名称"}, "newClientNo": {"type": "string", "description": "新客户号"}, "oldClientName": {"type": "string", "description": "原客户名称"}, "oldClientNo": {"type": "string", "description": "原客户号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"mergeNo": {"type": "string", "description": "合并编号"}}}}}}}, "/cif/nfin/client/merge/retry": {"post": {"tags": null, "summary": "客户合并错误重试接口", "description": "客户合并后，对失败的数据进行重试", "operationId": "ICore12009123", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"mergeNo": {"type": "string", "description": "合并编号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/merge/statusupdate": {"post": {"tags": null, "summary": "客户合并处理状态修改", "description": "客户合并处理状态修改接口，供其它系统修改客户何必跟处理状态", "operationId": "ICore12009122", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"errorDesc": {"type": "string", "description": "错误描述"}, "mergeModuleStatus": {"type": "string", "description": "客户合并的模块状态"}, "mergeNo": {"type": "string", "description": "合并编号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/private/jsmaint": {"post": {"tags": null, "summary": "对私客户信息维护简化版", "description": "对客户信息中的少量常用信息进行维护，主要用于外围系统发生客户信息修改时，联机发送核心报文给核心进行客户信息同步", "operationId": "ICore12009103", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"cifVerFlag": {"type": "string", "description": "客户级核实标志"}, "clientNo": {"type": "string", "description": "客户号"}, "contactTel": {"type": "string", "description": "联系电话  "}, "contactType": {"type": "string", "description": "联系类型"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "infoLack": {"type": "string", "description": "资料不全标志"}, "issCity": {"type": "string", "description": "签发城市"}, "issCountry": {"type": "string", "description": "发证国家"}, "issDate": {"type": "string", "description": "签发日期"}, "issPlace": {"type": "string", "description": "签发地"}, "issState": {"type": "string", "description": "签发省、州"}, "issueBranch": {"type": "string", "description": "签发机构"}, "maturityDate": {"type": "string", "description": "到期日期"}, "payAgentFlag": {"type": "string", "description": "代付标志"}, "phoneRepeatDeal": {"type": "string", "description": "客户移动电话手机号重复处理标识"}, "recAgentFlag": {"type": "string", "description": "代收标志"}, "route": {"type": "string", "description": "联系方式类型"}, "wrnFlag": {"type": "string", "description": "贷款核销标志"}, "ydtFlag": {"type": "string", "description": "客户级易贷通标志"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/private/main/status": {"post": {"tags": null, "summary": "客户状态维护", "description": "根据客户号修改客户状态(印尼项目)", "operationId": "ICore12009109", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "clientStatus": {"type": "string", "description": "客户状态"}, "flowCreatTime": {"type": "string", "description": "柜面工作流创建时间"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/private/maint": {"post": {"tags": null, "summary": "对私客户信息维护", "description": "个人客户信息新增修改，包括客户全量基本信息，证件信息，联系信息等", "operationId": "ICore12009106", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"accountOpenPurpose": {"type": "string", "description": "开户原因"}, "acctExec": {"type": "string", "description": "客户经理"}, "address": {"type": "string", "description": "地址"}, "amlCheckFlag": {"type": "string", "description": "反洗钱检查标志"}, "amlLastReviewDate": {"type": "string", "description": "AML最后核查时间"}, "amlNextReviewDate": {"type": "string", "description": "AML下次核查时间"}, "birthDate": {"type": "string", "description": "出生日期"}, "blacklistIndFlag": {"type": "string", "description": "是否黑名单客户"}, "blockGroupFlag": {"type": "string", "description": "是否群冻结"}, "blockReason": {"type": "string", "description": "冻结原因"}, "borrowerGrade": {"type": "string", "description": "借款人等级"}, "business": {"type": "string", "description": "行业代码"}, "categoryType": {"type": "string", "description": "客户细分类型"}, "chGivenName": {"type": "string", "description": "中文名"}, "chSurname": {"type": "string", "description": "中文姓"}, "checkDocumentId": {"type": "string", "description": "三证合一校验证件号"}, "checkDocumentType": {"type": "string", "description": "三证合一校验证件类型"}, "childNum": {"type": "string", "description": "孩子人数"}, "cifClientNraDetailArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"nraCountry": {"type": "string", "description": "非居民居民国（地区）"}, "nraIdTax": {"type": "string", "description": "非居民纳税人识别号"}, "nraIdTaxDetres": {"type": "string", "description": "未取得纳税人识别号具体原因"}, "nraIdTaxRes": {"type": "string", "description": "非居民未提供纳税人识别号原因"}}}}, "cifVerFlag": {"type": "string", "description": "客户级核实标志"}, "class1": {"type": "string", "description": "分类1"}, "class2": {"type": "string", "description": "分类2"}, "class3": {"type": "string", "description": "分类3"}, "class4": {"type": "string", "description": "分类4"}, "class5": {"type": "string", "description": "分类5"}, "classLevel": {"type": "string", "description": "综合评级"}, "classLevelDate": {"type": "string", "description": "评级日期"}, "clientAlias": {"type": "string", "description": "别名"}, "clientCity": {"type": "string", "description": "城市代码"}, "clientGroup": {"type": "string", "description": "客户群组\n"}, "clientGrp": {"type": "string", "description": "客户组"}, "clientIndicator": {"type": "string", "description": "客户标识"}, "clientMnemonic": {"type": "string", "description": "助记名称"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientOption": {"type": "string", "description": "客户操作类型"}, "clientPoint": {"type": "string", "description": "客户积分"}, "clientShort": {"type": "string", "description": "客户简称"}, "clientSpread": {"type": "number", "description": "客户浮动", "format": "double"}, "clientTranStatus": {"type": "string", "description": "客户交易状态"}, "clientType": {"type": "string", "description": "客户类型"}, "closeReason": {"type": "string", "description": "注销原因"}, "commissionClientName": {"type": "string", "description": "代办人名称"}, "commissionDocumentId": {"type": "string", "description": "代办人证件号码"}, "commissionDocumentType": {"type": "string", "description": "代办人证件类型"}, "commissionExpireDate": {"type": "string", "description": "代办人证件到期日"}, "company": {"type": "string", "description": "法人"}, "conNameCh": {"type": "string", "description": "控制人姓名（中文） "}, "conNameEn": {"type": "string", "description": "控制人姓名（英文） "}, "contactInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"address": {"type": "string", "description": "地址"}, "address1": {"type": "string", "description": "地址1"}, "city": {"type": "string", "description": "城市"}, "cityDist": {"type": "string", "description": "区/县"}, "cityTel": {"type": "string", "description": "电话区号"}, "clientOption": {"type": "string", "description": "客户操作类型"}, "contactTel": {"type": "string", "description": "联系电话  "}, "contactType": {"type": "string", "description": "联系类型"}, "country": {"type": "string", "description": "国家"}, "countryTel": {"type": "string", "description": "国家电话区号"}, "email": {"type": "string", "description": "电子邮件"}, "linkman": {"type": "string", "description": "对账联系人"}, "mobilePhone": {"type": "string", "description": "移动电话"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "postalCode": {"type": "string", "description": "邮政编码"}, "prefFlag": {"type": "string", "description": "首选标志"}, "route": {"type": "string", "description": "联系方式类型"}, "rt": {"type": "string", "description": "地区编号"}, "rw": {"type": "string", "description": "地区编码"}, "salutation": {"type": "string", "description": "称呼"}, "state": {"type": "string", "description": "省别代码"}, "subDistrict": {"type": "string", "description": "子区"}}}}, "contributeDegree": {"type": "string", "description": "贡献度"}, "countryCitizen": {"type": "string", "description": "居住国家"}, "countryLoc": {"type": "string", "description": "国籍"}, "countryRisk": {"type": "string", "description": "风险控制国家"}, "crRating": {"type": "string", "description": "客户信用等级"}, "ctrlBranch": {"type": "string", "description": "控制分行"}, "debtorCategory": {"type": "string", "description": "借款人类别"}, "dependentNum": {"type": "string", "description": "供养人数"}, "district": {"type": "string", "description": "区号"}, "documentInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"distCode": {"type": "string", "description": "地区代码"}, "distCodeDesc": {"type": "string", "description": "地区代码描述"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "inspectDate": {"type": "string", "description": "上次核查日期"}, "issCity": {"type": "string", "description": "签发城市"}, "issCountry": {"type": "string", "description": "发证国家"}, "issDate": {"type": "string", "description": "签发日期"}, "issPlace": {"type": "string", "description": "签发地"}, "issState": {"type": "string", "description": "签发省、州"}, "issueBranch": {"type": "string", "description": "签发机构"}, "maturityDate": {"type": "string", "description": "到期日期"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "prefFlag": {"type": "string", "description": "首选标志"}, "visaType": {"type": "string", "description": "签证类型"}}}}, "education": {"type": "string", "description": "教育程度编号"}, "employerFlag": {"type": "string", "description": "员工标志"}, "employerIndustry": {"type": "string", "description": "雇主所在行业"}, "employerName": {"type": "string", "description": "工作单位"}, "employmentStartDate": {"type": "string", "description": "雇佣开始日期"}, "enClientName": {"type": "string", "description": "客户英文名称"}, "firstName": {"type": "string", "description": "名"}, "flowCreatTime": {"type": "string", "description": "柜面工作流创建时间"}, "ftfFlag": {"type": "string", "description": "个人非居民标志"}, "givenName": {"type": "string", "description": "英文名"}, "groupName": {"type": "string", "description": "账户组名称"}, "hobby": {"type": "string", "description": "兴趣爱好"}, "incProofDate": {"type": "string", "description": "收入验证日期"}, "incProofInd": {"type": "string", "description": "收入验证标识"}, "incProofUserId": {"type": "string", "description": "收入验证人"}, "industry": {"type": "string", "description": "通用行业代码"}, "infoLack": {"type": "string", "description": "资料不全标志"}, "inlandOffshore": {"type": "string", "description": "境内境外标志"}, "internalIndFlag": {"type": "string", "description": "内部客户标志"}, "intraGroupFlag": {"type": "string", "description": "跨群组标志"}, "isSave": {"type": "string", "description": "留存标志"}, "kycFlag": {"type": "string", "description": "kyc标志"}, "lastName": {"type": "string", "description": "姓"}, "maidenName": {"type": "string", "description": "婚前名"}, "maintainType": {"type": "string", "description": "维护方式"}, "manageContent": {"type": "string", "description": "监管内容"}, "maritalStatus": {"type": "string", "description": "婚姻状况"}, "maxDegree": {"type": "string", "description": "最高学位"}, "midName": {"type": "string", "description": "中间名"}, "monMortgage": {"type": "number", "description": "月抵押付给金额", "format": "double"}, "monRental": {"type": "number", "description": "月租金", "format": "double"}, "monSalary": {"type": "number", "description": "月薪", "format": "double"}, "mortgageCcy": {"type": "string", "description": "抵押币种"}, "mothersMaidenName": {"type": "string", "description": "母亲婚前名"}, "mothersName": {"type": "string", "description": "母亲姓名"}, "nameSuffix": {"type": "string", "description": "客户名后缀"}, "narrative": {"type": "string", "description": "摘要"}, "nation": {"type": "string", "description": "民族"}, "negNonFinFlag": {"type": "string", "description": "消极非金融标识"}, "npwpFlag": {"type": "string", "description": "税务标志"}, "npwpNumber": {"type": "string", "description": "税务编号"}, "nraBirthCh": {"type": "string", "description": "非居民出生地（中文）"}, "nraBirthEn": {"type": "string", "description": "非居民出生地（英文）"}, "nraBitrhday": {"type": "string", "description": "非居民出生日期"}, "nraCountry": {"type": "string", "description": "非居民居民国（地区）"}, "nraNowAddressCh": {"type": "string", "description": "非居民现居地址（中文）"}, "nraNowAddressEn": {"type": "string", "description": "非居民现居地址（英文）"}, "occupationCode": {"type": "string", "description": "职业"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "original": {"type": "number", "description": "违约概率", "format": "double"}, "otherDocument": {"type": "string", "description": "其他证件名称"}, "passportType": {"type": "string", "description": "护照类型"}, "payAgentFlag": {"type": "string", "description": "代付标志"}, "pcpGroupId": {"type": "string", "description": "资金池账户组ID"}, "pep": {"type": "string", "description": "AML政要级别"}, "pepCategory": {"type": "string", "description": "政治公众人物细类"}, "pepClient": {"type": "string", "description": "政治公众人物"}, "pepClose": {"type": "string", "description": "政治公众人物亲密合作方"}, "pepCountry": {"type": "string", "description": "政治公众人物所在国家"}, "pepIndFlag": {"type": "string", "description": "PEP客户标志"}, "pepInter": {"type": "string", "description": "政治公众人物所在国际组织"}, "pepRisk": {"type": "string", "description": "政治任务风险"}, "pepType": {"type": "string", "description": "政治公众人物分类"}, "phoneRepeatDeal": {"type": "string", "description": "客户移动电话手机号重复处理标识"}, "placeOfBirth": {"type": "string", "description": "出生国"}, "post": {"type": "string", "description": "职务"}, "postalCode": {"type": "string", "description": "邮政编码"}, "prenupialAgreement": {"type": "string", "description": "婚前协议"}, "printLanguage": {"type": "string", "description": "打印语言"}, "profitCenter": {"type": "string", "description": "利润中心 "}, "qualification": {"type": "string", "description": "专业职称"}, "race": {"type": "string", "description": "种族"}, "raintg": {"type": "string", "description": "评级结果"}, "recAgentFlag": {"type": "string", "description": "代收标志"}, "redcrossNo": {"type": "string", "description": "红十字会员编号"}, "relationshipToBank": {"type": "string", "description": "是否关联"}, "remark": {"type": "string", "description": "备注"}, "rentalCcy": {"type": "string", "description": "租金币种"}, "residentDate": {"type": "string", "description": "入住日期"}, "residentFlag": {"type": "string", "description": "居民标识"}, "residentStatus": {"type": "string", "description": "居住状态"}, "residentType": {"type": "string", "description": "居住类型"}, "riskClassifications": {"type": "string", "description": "风险分类"}, "riskRatingExpiryDate": {"type": "string", "description": "风险评级有效日期"}, "riskRatingReviewDate": {"type": "string", "description": "风险评级审查日期"}, "riskScore": {"type": "string", "description": "AML风险评分"}, "riskWeight": {"type": "string", "description": "风险权重"}, "salaryAcctBranch": {"type": "string", "description": "工资账户开户行"}, "salaryAcctNo": {"type": "string", "description": "工资账号"}, "salaryCcy": {"type": "string", "description": "薪资币种"}, "salutation": {"type": "string", "description": "称呼"}, "selfStatement": {"type": "string", "description": "取得自证声明标志"}, "sex": {"type": "string", "description": "性别"}, "socialInsuNo": {"type": "string", "description": "社会保险号"}, "sourceOfIncome": {"type": "string", "description": "收入来源"}, "sourceType": {"type": "string", "description": "渠道类型"}, "specialRateFlag": {"type": "string", "description": "特殊利率"}, "spokenLanguage": {"type": "string", "description": "交流语言"}, "spouseDateOfBirth": {"type": "string", "description": "配偶生日"}, "spouseDocumentId": {"type": "string", "description": "配偶证件号码"}, "spouseName": {"type": "string", "description": "配偶姓名"}, "spouseRealEstateContract": {"type": "string", "description": "配偶房产合同"}, "state": {"type": "string", "description": "省别代码"}, "surname": {"type": "string", "description": "英文姓"}, "surnameFirst": {"type": "string", "description": "是否姓在前"}, "taxCountry": {"type": "string", "description": "税收居民国"}, "taxFlag": {"type": "string", "description": "是否税信息"}, "taxRemark": {"type": "string", "description": "税收备注"}, "taxableInd": {"type": "string", "description": "收税标志"}, "taxpayerId": {"type": "string", "description": "纳税人识别号"}, "tempClient": {"type": "string", "description": "临时客户标志"}, "treatment": {"type": "string", "description": "处置方式"}, "unprovideIdReason": {"type": "string", "description": "未提供识别号原因"}, "unverificationReason": {"type": "string", "description": "无法核实原因"}, "userDefineArray": {"type": "array", "description": "用户自定义数据表", "items": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "columnCode": {"type": "string", "description": "字段编码"}, "columnLength": {"type": "string", "description": "字段长度"}, "columnName": {"type": "string", "description": "列名"}, "columnType": {"type": "string", "description": "字段类型"}, "columnValue": {"type": "string", "description": "取值范围"}, "option": {"type": "string", "description": "操作类型"}}}}, "verificationResult": {"type": "string", "description": "核查结果"}, "writtenLanguage": {"type": "string", "description": "书写语言"}, "yearlyIncome": {"type": "number", "description": "年收入", "format": "double"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}}}}}, "/cif/nfin/client/private/synchrodata": {"post": {"tags": null, "summary": "对私客户信息同步到核心-外围", "description": "对私客户信息同步到核心，专供其他系统使用，将外系统客户信息的维护更新同步到核心中", "operationId": "ICore12009222", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"acctVerifyState": {"type": "string", "description": "客户核实状态"}, "birthDate": {"type": "string", "description": "出生日期"}, "categoryType": {"type": "string", "description": "客户细分类型"}, "city": {"type": "string", "description": "城市"}, "cityDist": {"type": "string", "description": "区/县"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientStatus": {"type": "string", "description": "客户状态"}, "clientType": {"type": "string", "description": "客户类型"}, "company": {"type": "string", "description": "法人"}, "countryLoc": {"type": "string", "description": "国籍"}, "ctrlBranch": {"type": "string", "description": "控制分行"}, "documentInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "issCountry": {"type": "string", "description": "发证国家"}, "issDate": {"type": "string", "description": "签发日期"}, "maturityDate": {"type": "string", "description": "到期日期"}}}}, "inlandOffshore": {"type": "string", "description": "境内境外标志"}, "internalIndFlag": {"type": "string", "description": "内部客户标志"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "sex": {"type": "string", "description": "性别"}, "state": {"type": "string", "description": "省别代码"}, "taxResidentFlag": {"type": "string", "description": "税收居民标识"}, "wrnFlag": {"type": "string", "description": "贷款核销标志"}, "ydtFlag": {"type": "string", "description": "客户级易贷通标志"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/public/maint": {"post": {"tags": null, "summary": "对公客户信息维护", "description": "对公客户信息新增修改，包括客户全量基本信息，证件信息，联系信息等", "operationId": "ICore12009107", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"acctExec": {"type": "string", "description": "客户经理"}, "address": {"type": "string", "description": "地址"}, "authCapital": {"type": "number", "description": "注册资本", "format": "double"}, "bankCode": {"type": "string", "description": "银行代码"}, "basicAcctNo": {"type": "string", "description": "基本账号"}, "basicAcctOpenat": {"type": "string", "description": "基本账户开户行"}, "blacklistIndFlag": {"type": "string", "description": "是否黑名单客户"}, "blockGroupFlag": {"type": "string", "description": "是否群冻结"}, "blockReason": {"type": "string", "description": "冻结原因"}, "borrowerGrade": {"type": "string", "description": "借款人等级"}, "branch": {"type": "string", "description": "所属机构号"}, "branchInnerFlag": {"type": "string", "description": "是否机构内客户标志"}, "busilicenceStatus": {"type": "string", "description": "营业执照状态"}, "business": {"type": "string", "description": "行业代码"}, "businessScope": {"type": "string", "description": "经营范围"}, "capitalCcy": {"type": "string", "description": "注册资本币种"}, "categoryType": {"type": "string", "description": "客户细分类型"}, "centralBankRef": {"type": "string", "description": "中央银行"}, "cessation": {"type": "string", "description": "终止类型"}, "cessationDate": {"type": "string", "description": "终止日期"}, "checkDate": {"type": "string", "description": "检查日期"}, "cifBenefitOwnerInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"address": {"type": "string", "description": "地址"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "isSpecialPeople": {"type": "string", "description": "是否特定自然人"}, "maturityDate": {"type": "string", "description": "到期日期"}, "ownerName": {"type": "string", "description": "所有人姓名"}}}}, "cifClientSuperCropArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"superCropAcctCheckNo": {"type": "string", "description": "上级法人或主管单位基本存款账户开户许可证核准号"}, "superCropDocExpiryDate": {"type": "string", "description": "上级法人或主管单位证件有效期"}, "superCropName": {"type": "string", "description": "上级法人或主管单位名称"}, "superCropOrgCode": {"type": "string", "description": "上级法人或主管单位组织机构代码"}, "superDocExpiryDate": {"type": "string", "description": "上级法人或单位负责人证件有效期"}, "superDocumentId": {"type": "string", "description": "上级法人或单位负责人证件号码"}, "superDocumentType": {"type": "string", "description": "上级法人或单位负责人证件种类"}, "superName": {"type": "string", "description": "上级法人或单位负责人姓名"}}}}, "cifContactArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "linkmanName": {"type": "string", "description": "联系人名称"}, "linkmanType": {"type": "string", "description": "联系人类型"}, "phoneNo1": {"type": "string", "description": "电话号码 1"}, "phoneNo2": {"type": "string", "description": "联系人电话2"}}}}, "cifCropCompnayInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"companyGovernorName": {"type": "string", "description": "法人/财务主管名称 "}, "compnayGovernor": {"type": "string", "description": "法人/财务主管 "}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "maturityDate": {"type": "string", "description": "到期日期"}}}}, "cifRealControlInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"conDocumentId": {"type": "string", "description": "控制人证件号码 "}, "conDocumentType": {"type": "string", "description": "控制人证件类型 "}, "conName": {"type": "string", "description": "控制人姓名 "}, "maturityDate": {"type": "string", "description": "到期日期"}}}}, "cifVerFlag": {"type": "string", "description": "客户级核实标志"}, "class1": {"type": "string", "description": "分类1"}, "class2": {"type": "string", "description": "分类2"}, "class3": {"type": "string", "description": "分类3"}, "class4": {"type": "string", "description": "分类4"}, "class5": {"type": "string", "description": "分类5"}, "classLevel": {"type": "string", "description": "综合评级"}, "classLevelDate": {"type": "string", "description": "评级日期"}, "clientAlias": {"type": "string", "description": "别名"}, "clientCity": {"type": "string", "description": "城市代码"}, "clientGroup": {"type": "string", "description": "客户群组\n"}, "clientGrp": {"type": "string", "description": "客户组"}, "clientIndicator": {"type": "string", "description": "客户标识"}, "clientMnemonic": {"type": "string", "description": "助记名称"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientOption": {"type": "string", "description": "客户操作类型"}, "clientPoint": {"type": "string", "description": "客户积分"}, "clientShort": {"type": "string", "description": "客户简称"}, "clientSpread": {"type": "number", "description": "客户浮动", "format": "double"}, "clientTranStatus": {"type": "string", "description": "客户交易状态"}, "clientType": {"type": "string", "description": "客户类型"}, "commissionClientName": {"type": "string", "description": "代办人名称"}, "commissionDocumentId": {"type": "string", "description": "代办人证件号码"}, "commissionDocumentType": {"type": "string", "description": "代办人证件类型"}, "commissionExpireDate": {"type": "string", "description": "代办人证件到期日"}, "company": {"type": "string", "description": "法人"}, "companySecretaryFlag": {"type": "string", "description": "是否指定公司秘书"}, "conBirthCh": {"type": "string", "description": "控制人出生地（中文）  "}, "conBirthEn": {"type": "string", "description": "控制人出生地（英文）  "}, "conBitrhday": {"type": "string", "description": "控制人出生日期  "}, "conCountry": {"type": "string", "description": "控制人居民国（地区）  "}, "conIdTax": {"type": "string", "description": "控制人纳税人识别号  "}, "conIdTaxRes": {"type": "string", "description": "控制人纳税人识别原因  "}, "conNameCh": {"type": "string", "description": "控制人姓名（中文） "}, "conNameEn": {"type": "string", "description": "控制人姓名（英文） "}, "conNowAddressCh": {"type": "string", "description": "控制人现居地址（中文）  "}, "conNowAddressEn": {"type": "string", "description": "控制人现居地址（英文）  "}, "contactInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"addrMode": {"type": "string", "description": "地址模式"}, "address": {"type": "string", "description": "地址"}, "address1": {"type": "string", "description": "地址1"}, "bicCode": {"type": "string", "description": "BIC代码"}, "city": {"type": "string", "description": "城市"}, "cityDist": {"type": "string", "description": "区/县"}, "cityTel": {"type": "string", "description": "电话区号"}, "contactTel": {"type": "string", "description": "联系电话  "}, "contactType": {"type": "string", "description": "联系类型"}, "country": {"type": "string", "description": "国家"}, "countryTel": {"type": "string", "description": "国家电话区号"}, "linkman": {"type": "string", "description": "对账联系人"}, "mobilePhone": {"type": "string", "description": "移动电话"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "postalCode": {"type": "string", "description": "邮政编码"}, "prefFlag": {"type": "string", "description": "首选标志"}, "route": {"type": "string", "description": "联系方式类型"}, "salutation": {"type": "string", "description": "称呼"}, "state": {"type": "string", "description": "省别代码"}}}}, "contributeDegree": {"type": "string", "description": "贡献度"}, "controlContactInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"contactAddressType": {"type": "string", "description": "联系地址类型 "}, "controlCity": {"type": "string", "description": "控制人所在城市"}, "controlContactTel": {"type": "string", "description": "控制人联系电话  "}, "controlCountry": {"type": "string", "description": "控制人国家"}, "controlState": {"type": "string", "description": "控制人省、州"}}}}, "controlDept": {"type": "string", "description": "控制人部门"}, "controlTaxBirthday": {"type": "string", "description": "控制人出生日期"}, "controlTaxCountry": {"type": "string", "description": "控制人税收居民国"}, "controlTaxFlag": {"type": "string", "description": "控制人税收居民标识"}, "controlTaxName": {"type": "string", "description": "控制人客户姓名"}, "controlTaxpayerId": {"type": "string", "description": "控制人纳税人识别号"}, "controlUnprovideIdReason": {"type": "string", "description": "控制人未提供识别号原因"}, "corpFlag": {"type": "string", "description": "是否小微企业"}, "corpPlan": {"type": "string", "description": "公司计划  "}, "corpSize": {"type": "string", "description": "企业规模 "}, "countryCitizen": {"type": "string", "description": "居住国家"}, "countryLoc": {"type": "string", "description": "国籍"}, "countryRisk": {"type": "string", "description": "风险控制国家"}, "crRating": {"type": "string", "description": "客户信用等级"}, "ctrlBranch": {"type": "string", "description": "控制分行"}, "directorInd": {"type": "string", "description": "是否指定银行负责人"}, "district": {"type": "string", "description": "区号"}, "documentInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"clientOption": {"type": "string", "description": "客户操作类型"}, "distCode": {"type": "string", "description": "地区代码"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "inspectDate": {"type": "string", "description": "上次核查日期"}, "issCity": {"type": "string", "description": "签发城市"}, "issCountry": {"type": "string", "description": "发证国家"}, "issDate": {"type": "string", "description": "签发日期"}, "issPlace": {"type": "string", "description": "签发地"}, "issState": {"type": "string", "description": "签发省、州"}, "issueBranch": {"type": "string", "description": "签发机构"}, "maturityDate": {"type": "string", "description": "到期日期"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "prefFlag": {"type": "string", "description": "首选标志"}}}}, "econDist": {"type": "string", "description": "经济特区"}, "econType": {"type": "string", "description": "经济类型"}, "empNum": {"type": "string", "description": "员工数"}, "enClientName": {"type": "string", "description": "客户英文名称"}, "exposureCap": {"type": "string", "description": "风险控制标志"}, "finAppCode": {"type": "string", "description": "金融机构许可证号"}, "fitch": {"type": "string", "description": "Fitch等级"}, "foreRemitCerAvai": {"type": "string", "description": "外汇证有效期  "}, "foreignAppNo": {"type": "string", "description": "外商投资批准证书号"}, "fxIssOrgan": {"type": "string", "description": "外汇等级证号"}, "fxIssPlace": {"type": "string", "description": "外汇登记证签发地"}, "fxRegisterId": {"type": "string", "description": "外汇登记证"}, "higherOrgan": {"type": "string", "description": "主管单位"}, "identifyFlag": {"type": "string", "description": "是否免于识别"}, "incorDate": {"type": "string", "description": "公司成立日期"}, "industry": {"type": "string", "description": "通用行业代码"}, "infoLack": {"type": "string", "description": "资料不全标志"}, "inlandOffshore": {"type": "string", "description": "境内境外标志"}, "inpExpNo": {"type": "string", "description": "进出口业务经营资格编号"}, "internalIndFlag": {"type": "string", "description": "内部客户标志"}, "investor": {"type": "string", "description": "投资人"}, "legalRep": {"type": "string", "description": "法定代表人名称"}, "lendingOfficerInd": {"type": "string", "description": "是否指定贷款副负责人"}, "loanCardId": {"type": "string", "description": "该企业贷款使用的贷款卡编码"}, "loanGrade": {"type": "string", "description": "贷方级别"}, "marketParticipant": {"type": "string", "description": "市场参与者"}, "minorityInterest": {"type": "string", "description": "是否最小控股,不能超过10%的股权"}, "moodyRate": {"type": "number", "description": "外部浮动利率", "format": "double"}, "narrative": {"type": "string", "description": "摘要"}, "negNonFinFlag": {"type": "string", "description": "消极非金融标识"}, "nraBitrhday": {"type": "string", "description": "非居民出生日期"}, "nraCountry": {"type": "string", "description": "非居民居民国（地区）"}, "nraIdTax": {"type": "string", "description": "非居民纳税人识别号"}, "nraIdTaxRes": {"type": "string", "description": "非居民未提供纳税人识别号原因"}, "nraNowAddressCh": {"type": "string", "description": "非居民现居地址（中文）"}, "nraNowAddressEn": {"type": "string", "description": "非居民现居地址（英文）"}, "offWebsite": {"type": "string", "description": "官方网站"}, "operateCountry": {"type": "string", "description": "运营国家"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "organ": {"type": "string", "description": "组织机构代码"}, "originCountry": {"type": "string", "description": "注册国家"}, "paidCapitalCcy": {"type": "string", "description": "实收资本币种"}, "paidUpCapital": {"type": "number", "description": "实收资本", "format": "double"}, "pd": {"type": "number", "description": "违约机率", "format": "double"}, "pepIndFlag": {"type": "string", "description": "PEP客户标志"}, "phoneFax": {"type": "string", "description": "是否电话/传真指令指定客户"}, "phoneFaxAcct": {"type": "string", "description": "是否电话/传真指令指定账户客户"}, "postalCode": {"type": "string", "description": "邮政编码"}, "printLanguage": {"type": "string", "description": "打印语言"}, "profitCenter": {"type": "string", "description": "利润中心 "}, "refIntermediaryFlag": {"type": "string", "description": "中介推崇标志"}, "registerNo": {"type": "string", "description": "登记注册号"}, "registerNoType": {"type": "string", "description": "登记注册号类型"}, "remark": {"type": "string", "description": "备注"}, "repDocumentId": {"type": "string", "description": "法定代表人身份证件号码"}, "repDocumentType": {"type": "string", "description": "法定代表人身份证件类型"}, "repExpiryDate": {"type": "string", "description": "法人代表证件到期日"}, "repIssDate": {"type": "string", "description": "法人证件签发日期"}, "repPhone": {"type": "string", "description": "法人代表手机号"}, "riskWeight": {"type": "string", "description": "风险权重"}, "selfStatement": {"type": "string", "description": "取得自证声明标志"}, "sourceType": {"type": "string", "description": "渠道类型"}, "spRate": {"type": "string", "description": "SP等级  "}, "specialAppNo": {"type": "string", "description": "特殊行业许可证书号"}, "spokenLanguage": {"type": "string", "description": "交流语言"}, "state": {"type": "string", "description": "省别代码"}, "subDirectorInd": {"type": "string", "description": "指定贷款负责人标志"}, "subLendingOfficerInd": {"type": "string", "description": "指定银行副负责人标志"}, "sucFlag": {"type": "string", "description": "社会统一信用代码标志"}, "swiftId": {"type": "string", "description": "银行国际代码"}, "taxCerAvai": {"type": "string", "description": "税务证有效期"}, "taxCountry": {"type": "string", "description": "税收居民国"}, "taxFileNo": {"type": "string", "description": "国税登记号"}, "taxFlag": {"type": "string", "description": "是否税信息"}, "taxOrgType": {"type": "string", "description": "税收机构类型"}, "taxRemark": {"type": "string", "description": "税收备注"}, "taxableInd": {"type": "string", "description": "收税标志"}, "taxpayerAddress": {"type": "string", "description": "纳税人地址"}, "taxpayerId": {"type": "string", "description": "纳税人识别号"}, "taxpayerName": {"type": "string", "description": "纳税人名称"}, "taxpayerType": {"type": "string", "description": "纳税人类型"}, "tempClient": {"type": "string", "description": "临时客户标志"}, "tranEmail": {"type": "string", "description": "交易用EMAIL"}, "unprovideIdReason": {"type": "string", "description": "未提供识别号原因"}, "userDefineArray": {"type": "array", "description": "用户自定义数据表", "items": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "columnCode": {"type": "string", "description": "字段编码"}, "columnLength": {"type": "string", "description": "字段长度"}, "columnName": {"type": "string", "description": "列名"}, "columnType": {"type": "string", "description": "字段类型"}, "columnValue": {"type": "string", "description": "取值范围"}, "option": {"type": "string", "description": "操作类型"}}}}, "voucherType": {"type": "string", "description": "开票类型"}, "writtenLanguage": {"type": "string", "description": "书写语言"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}}}}}, "/cif/nfin/client/public/maint/new": {"post": {"tags": null, "summary": "对公客户信息维护(修改版)", "description": "对公客户信息新增修改，包括客户全量基本信息，证件信息，联系信息等", "operationId": "ICore12009114", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"acctExec": {"type": "string", "description": "客户经理"}, "acctExecCode": {"type": "string", "description": "客户经理代码"}, "address": {"type": "string", "description": "地址"}, "amlCheckFlag": {"type": "string", "description": "反洗钱检查标志"}, "areaName": {"type": "string", "description": "区域名称"}, "authCapital": {"type": "number", "description": "注册资本", "format": "double"}, "bankCode": {"type": "string", "description": "银行代码"}, "basicAcctNo": {"type": "string", "description": "基本账号"}, "basicAcctOpenat": {"type": "string", "description": "基本账户开户行"}, "blacklistIndFlag": {"type": "string", "description": "是否黑名单客户"}, "blankShare": {"type": "string", "description": "不记名股票"}, "blockGroupFlag": {"type": "string", "description": "是否群冻结"}, "blockReason": {"type": "string", "description": "冻结原因"}, "borrowerGrade": {"type": "string", "description": "借款人等级"}, "branch": {"type": "string", "description": "所属机构号"}, "branchInnerFlag": {"type": "string", "description": "是否机构内客户标志"}, "busilicenceStatus": {"type": "string", "description": "营业执照状态"}, "business": {"type": "string", "description": "行业代码"}, "businessPartner": {"type": "string", "description": "业务合作伙伴"}, "businessScope": {"type": "string", "description": "经营范围"}, "capitalCcy": {"type": "string", "description": "注册资本币种"}, "categoryType": {"type": "string", "description": "客户细分类型"}, "centralBankRef": {"type": "string", "description": "中央银行"}, "cessation": {"type": "string", "description": "终止类型"}, "cessationDate": {"type": "string", "description": "终止日期"}, "checkDate": {"type": "string", "description": "检查日期"}, "cifBenefitOwnerInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"address": {"type": "string", "description": "地址"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "isSpecialPeople": {"type": "string", "description": "是否特定自然人"}, "maturityDate": {"type": "string", "description": "到期日期"}, "ownerName": {"type": "string", "description": "所有人姓名"}}}}, "cifClientSuperCropArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"superCropAcctCheckNo": {"type": "string", "description": "上级法人或主管单位基本存款账户开户许可证核准号"}, "superCropDocExpiryDate": {"type": "string", "description": "上级法人或主管单位证件有效期"}, "superCropName": {"type": "string", "description": "上级法人或主管单位名称"}, "superCropOrgCode": {"type": "string", "description": "上级法人或主管单位组织机构代码"}, "superDocExpiryDate": {"type": "string", "description": "上级法人或单位负责人证件有效期"}, "superDocumentId": {"type": "string", "description": "上级法人或单位负责人证件号码"}, "superDocumentType": {"type": "string", "description": "上级法人或单位负责人证件种类"}, "superName": {"type": "string", "description": "上级法人或单位负责人姓名"}}}}, "cifContactArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "linkmanName": {"type": "string", "description": "联系人名称"}, "linkmanType": {"type": "string", "description": "联系人类型"}, "phoneNo1": {"type": "string", "description": "电话号码 1"}, "phoneNo2": {"type": "string", "description": "联系人电话2"}}}}, "cifCropCompnayInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"companyGovernorName": {"type": "string", "description": "法人/财务主管名称 "}, "compnayGovernor": {"type": "string", "description": "法人/财务主管 "}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "maturityDate": {"type": "string", "description": "到期日期"}}}}, "cifRealControlInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"conDocumentId": {"type": "string", "description": "控制人证件号码 "}, "conDocumentType": {"type": "string", "description": "控制人证件类型 "}, "conName": {"type": "string", "description": "控制人姓名 "}, "maturityDate": {"type": "string", "description": "到期日期"}}}}, "cifVerFlag": {"type": "string", "description": "客户级核实标志"}, "class1": {"type": "string", "description": "分类1"}, "class2": {"type": "string", "description": "分类2"}, "class3": {"type": "string", "description": "分类3"}, "class4": {"type": "string", "description": "分类4"}, "class5": {"type": "string", "description": "分类5"}, "classLevel": {"type": "string", "description": "综合评级"}, "classLevelDate": {"type": "string", "description": "评级日期"}, "client": {"type": "string", "description": "客户"}, "clientAlias": {"type": "string", "description": "别名"}, "clientCity": {"type": "string", "description": "城市代码"}, "clientFullName": {"type": "string", "description": "客户全名称"}, "clientGroup": {"type": "string", "description": "客户群组\n"}, "clientGrp": {"type": "string", "description": "客户组"}, "clientIndicator": {"type": "string", "description": "客户标识"}, "clientMnemonic": {"type": "string", "description": "助记名称"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientOption": {"type": "string", "description": "客户操作类型"}, "clientPoint": {"type": "string", "description": "客户积分"}, "clientShort": {"type": "string", "description": "客户简称"}, "clientSpread": {"type": "number", "description": "客户浮动", "format": "double"}, "clientTranStatus": {"type": "string", "description": "客户交易状态"}, "clientType": {"type": "string", "description": "客户类型"}, "commissionClientName": {"type": "string", "description": "代办人名称"}, "commissionDocumentId": {"type": "string", "description": "代办人证件号码"}, "commissionDocumentType": {"type": "string", "description": "代办人证件类型"}, "commissionExpireDate": {"type": "string", "description": "代办人证件到期日"}, "company": {"type": "string", "description": "法人"}, "companyOwnIndividual": {"type": "string", "description": "由个人全资拥有的公司"}, "companySecretaryFlag": {"type": "string", "description": "是否指定公司秘书"}, "conBirthCh": {"type": "string", "description": "控制人出生地（中文）  "}, "conBirthEn": {"type": "string", "description": "控制人出生地（英文）  "}, "conBitrhday": {"type": "string", "description": "控制人出生日期  "}, "conCountry": {"type": "string", "description": "控制人居民国（地区）  "}, "conIdTax": {"type": "string", "description": "控制人纳税人识别号  "}, "conIdTaxRes": {"type": "string", "description": "控制人纳税人识别原因  "}, "conLendingParty": {"type": "string", "description": "关联贷款方"}, "conNameCh": {"type": "string", "description": "控制人姓名（中文） "}, "conNameEn": {"type": "string", "description": "控制人姓名（英文） "}, "conNowAddressCh": {"type": "string", "description": "控制人现居地址（中文）  "}, "conNowAddressEn": {"type": "string", "description": "控制人现居地址（英文）  "}, "conOfCust": {"type": "string", "description": "客户集中度"}, "conOfSupple": {"type": "string", "description": "供应商集中度"}, "contactInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"addrMode": {"type": "string", "description": "地址模式"}, "address": {"type": "string", "description": "地址"}, "address1": {"type": "string", "description": "地址1"}, "bicCode": {"type": "string", "description": "BIC代码"}, "city": {"type": "string", "description": "城市"}, "cityDist": {"type": "string", "description": "区/县"}, "cityTel": {"type": "string", "description": "电话区号"}, "contactTel": {"type": "string", "description": "联系电话  "}, "contactType": {"type": "string", "description": "联系类型"}, "country": {"type": "string", "description": "国家"}, "countryTel": {"type": "string", "description": "国家电话区号"}, "linkman": {"type": "string", "description": "对账联系人"}, "mobilePhone": {"type": "string", "description": "移动电话"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "postalCode": {"type": "string", "description": "邮政编码"}, "prefFlag": {"type": "string", "description": "首选标志"}, "route": {"type": "string", "description": "联系方式类型"}, "salutation": {"type": "string", "description": "称呼"}, "state": {"type": "string", "description": "省别代码"}}}}, "contraType": {"type": "string", "description": "对手方类型"}, "contributeDegree": {"type": "string", "description": "贡献度"}, "controlContactInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"contactAddressType": {"type": "string", "description": "联系地址类型 "}, "controlCity": {"type": "string", "description": "控制人所在城市"}, "controlContactTel": {"type": "string", "description": "控制人联系电话  "}, "controlCountry": {"type": "string", "description": "控制人国家"}, "controlState": {"type": "string", "description": "控制人省、州"}}}}, "controlDept": {"type": "string", "description": "控制人部门"}, "controlTaxBirthday": {"type": "string", "description": "控制人出生日期"}, "controlTaxCountry": {"type": "string", "description": "控制人税收居民国"}, "controlTaxFlag": {"type": "string", "description": "控制人税收居民标识"}, "controlTaxName": {"type": "string", "description": "控制人客户姓名"}, "controlTaxpayerId": {"type": "string", "description": "控制人纳税人识别号"}, "controlUnprovideIdReason": {"type": "string", "description": "控制人未提供识别号原因"}, "coreMarketFlag": {"type": "string", "description": "核心市场参与者"}, "corpFlag": {"type": "string", "description": "是否小微企业"}, "corpPlan": {"type": "string", "description": "公司计划  "}, "corpSize": {"type": "string", "description": "企业规模 "}, "corporateShare": {"type": "string", "description": "拥有海外个人公司股东的本地公司"}, "countryCitizen": {"type": "string", "description": "居住国家"}, "countryLoc": {"type": "string", "description": "国籍"}, "countryRisk": {"type": "string", "description": "风险控制国家"}, "crRating": {"type": "string", "description": "客户信用等级"}, "ctrlBranch": {"type": "string", "description": "控制分行"}, "customerDue": {"type": "string", "description": "经批准的中介机构介绍"}, "directUnchain": {"type": "string", "description": "所有权链中提名董事"}, "directorInd": {"type": "string", "description": "是否指定银行负责人"}, "district": {"type": "string", "description": "区号"}, "documentInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"clientOption": {"type": "string", "description": "客户操作类型"}, "distCode": {"type": "string", "description": "地区代码"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "inspectDate": {"type": "string", "description": "上次核查日期"}, "issCity": {"type": "string", "description": "签发城市"}, "issCountry": {"type": "string", "description": "发证国家"}, "issDate": {"type": "string", "description": "签发日期"}, "issPlace": {"type": "string", "description": "签发地"}, "issState": {"type": "string", "description": "签发省、州"}, "issueBranch": {"type": "string", "description": "签发机构"}, "maturityDate": {"type": "string", "description": "到期日期"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "prefFlag": {"type": "string", "description": "首选标志"}}}}, "econDist": {"type": "string", "description": "经济特区"}, "econType": {"type": "string", "description": "经济类型"}, "empNum": {"type": "string", "description": "员工数"}, "enClientName": {"type": "string", "description": "客户英文名称"}, "exposureCap": {"type": "string", "description": "风险控制标志"}, "extRatingMod": {"type": "string", "description": "外部评级穆迪"}, "extRatingNormal": {"type": "string", "description": "外部评级标普"}, "finAppCode": {"type": "string", "description": "金融机构许可证号"}, "fitch": {"type": "string", "description": "Fitch等级"}, "forInUnchain": {"type": "string", "description": "所有权链中的基金会"}, "foreRemitCerAvai": {"type": "string", "description": "外汇证有效期  "}, "foreignAppNo": {"type": "string", "description": "外商投资批准证书号"}, "formOfCompany": {"type": "string", "description": "公司形式"}, "ftfFlag": {"type": "string", "description": "个人非居民标志"}, "fxIssOrgan": {"type": "string", "description": "外汇等级证号"}, "fxIssPlace": {"type": "string", "description": "外汇登记证签发地"}, "fxRegisterId": {"type": "string", "description": "外汇登记证"}, "governmentRe": {"type": "string", "description": "政府关联标志"}, "groupName": {"type": "string", "description": "账户组名称"}, "guarantyAssetsClass": {"type": "string", "description": "担保人资产等级"}, "higherOrgan": {"type": "string", "description": "主管单位"}, "identifyFlag": {"type": "string", "description": "是否免于识别"}, "incorDate": {"type": "string", "description": "公司成立日期"}, "industry": {"type": "string", "description": "通用行业代码"}, "infoLack": {"type": "string", "description": "资料不全标志"}, "inlandOffshore": {"type": "string", "description": "境内境外标志"}, "innerRaintg": {"type": "string", "description": "内部评级"}, "inpExpNo": {"type": "string", "description": "进出口业务经营资格编号"}, "internalIndFlag": {"type": "string", "description": "内部客户标志"}, "intraGroupFlag": {"type": "string", "description": "跨群组标志"}, "investServiceFlag": {"type": "string", "description": "投资服务同意书标志"}, "investor": {"type": "string", "description": "投资人"}, "legalRep": {"type": "string", "description": "法定代表人名称"}, "lendingOfficerInd": {"type": "string", "description": "是否指定贷款副负责人"}, "liaIndicatorFlag": {"type": "string", "description": "诉讼标志"}, "loanCardId": {"type": "string", "description": "该企业贷款使用的贷款卡编码"}, "loanGrade": {"type": "string", "description": "贷方级别"}, "manageContent": {"type": "string", "description": "监管内容"}, "marketParticipant": {"type": "string", "description": "市场参与者"}, "minorityInterest": {"type": "string", "description": "是否最小控股,不能超过10%的股权"}, "modeOfInformation": {"type": "string", "description": "经营模式"}, "moodyRate": {"type": "number", "description": "外部浮动利率", "format": "double"}, "nameOfExchange": {"type": "string", "description": "交易所名称"}, "narrative": {"type": "string", "description": "摘要"}, "negNonFinFlag": {"type": "string", "description": "消极非金融标识"}, "noInterfaceClient": {"type": "string", "description": "非面对面客户"}, "nraBitrhday": {"type": "string", "description": "非居民出生日期"}, "nraCountry": {"type": "string", "description": "非居民居民国（地区）"}, "nraIdTax": {"type": "string", "description": "非居民纳税人识别号"}, "nraIdTaxRes": {"type": "string", "description": "非居民未提供纳税人识别号原因"}, "nraNowAddressCh": {"type": "string", "description": "非居民现居地址（中文）"}, "nraNowAddressEn": {"type": "string", "description": "非居民现居地址（英文）"}, "numberOfBranch": {"type": "string", "description": "分行数量"}, "offWebsite": {"type": "string", "description": "官方网站"}, "onlineSettleFlag": {"type": "string", "description": "线上清算标志\n"}, "operateCountry": {"type": "string", "description": "运营国家"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "organ": {"type": "string", "description": "组织机构代码"}, "originCountry": {"type": "string", "description": "注册国家"}, "original": {"type": "number", "description": "违约概率", "format": "double"}, "originalGrade": {"type": "string", "description": "撒"}, "originalStart": {"type": "number", "description": "初始违约概率", "format": "double"}, "ownershipStructure": {"type": "string", "description": "涉及三个或三个以上注册地的所有权结构"}, "paidCapitalCcy": {"type": "string", "description": "实收资本币种"}, "paidUpCapital": {"type": "number", "description": "实收资本", "format": "double"}, "pcpGroupId": {"type": "string", "description": "资金池账户组ID"}, "pd": {"type": "number", "description": "违约机率", "format": "double"}, "pepIndFlag": {"type": "string", "description": "PEP客户标志"}, "phoneFax": {"type": "string", "description": "是否电话/传真指令指定客户"}, "phoneFaxAcct": {"type": "string", "description": "是否电话/传真指令指定账户客户"}, "placeOfOperation": {"type": "string", "description": "业务开展地"}, "placeWhereBusi": {"type": "string", "description": "业务建立地"}, "postalCode": {"type": "string", "description": "邮政编码"}, "printLanguage": {"type": "string", "description": "打印语言"}, "profitCenter": {"type": "string", "description": "利润中心 "}, "raintg": {"type": "string", "description": "评级结果"}, "refIntermediaryFlag": {"type": "string", "description": "中介推崇标志"}, "registerNo": {"type": "string", "description": "登记注册号"}, "registerNoType": {"type": "string", "description": "登记注册号类型"}, "remark": {"type": "string", "description": "备注"}, "repDocumentId": {"type": "string", "description": "法定代表人身份证件号码"}, "repDocumentType": {"type": "string", "description": "法定代表人身份证件类型"}, "repExpiryDate": {"type": "string", "description": "法人代表证件到期日"}, "repIssDate": {"type": "string", "description": "法人证件签发日期"}, "repPhone": {"type": "string", "description": "法人代表手机号"}, "riskWeight": {"type": "string", "description": "风险权重"}, "selfStatement": {"type": "string", "description": "取得自证声明标志"}, "sharedUnchain": {"type": "string", "description": "所有权链中的提名股东"}, "socType": {"type": "string", "description": "主权类型"}, "sourceType": {"type": "string", "description": "渠道类型"}, "spRate": {"type": "string", "description": "SP等级  "}, "specialAppNo": {"type": "string", "description": "特殊行业许可证书号"}, "specialRateFlag": {"type": "string", "description": "特殊利率"}, "spokenLanguage": {"type": "string", "description": "交流语言"}, "startDate": {"type": "string", "description": "开始日期"}, "state": {"type": "string", "description": "省别代码"}, "subDirectorInd": {"type": "string", "description": "指定贷款负责人标志"}, "subLendingOfficerInd": {"type": "string", "description": "指定银行副负责人标志"}, "sucFlag": {"type": "string", "description": "社会统一信用代码标志"}, "swiftId": {"type": "string", "description": "银行国际代码"}, "swiftName": {"type": "string", "description": "贸易金融名称"}, "taxCerAvai": {"type": "string", "description": "税务证有效期"}, "taxCountry": {"type": "string", "description": "税收居民国"}, "taxFileNo": {"type": "string", "description": "国税登记号"}, "taxFlag": {"type": "string", "description": "是否税信息"}, "taxOrgType": {"type": "string", "description": "税收机构类型"}, "taxRemark": {"type": "string", "description": "税收备注"}, "taxableInd": {"type": "string", "description": "收税标志"}, "taxpayerAddress": {"type": "string", "description": "纳税人地址"}, "taxpayerId": {"type": "string", "description": "纳税人识别号"}, "taxpayerName": {"type": "string", "description": "纳税人名称"}, "taxpayerType": {"type": "string", "description": "纳税人类型"}, "telCheckerFlag": {"type": "string", "description": "恐怖组织标志"}, "tempClient": {"type": "string", "description": "临时客户标志"}, "threeOrLayer": {"type": "string", "description": "三层或三层以上股权结构"}, "tranEmail": {"type": "string", "description": "交易用EMAIL"}, "trustInUnchain": {"type": "string", "description": "所有权链中的信托"}, "unprovideIdReason": {"type": "string", "description": "未提供识别号原因"}, "unresInvestFund": {"type": "string", "description": "不受监管的投资基金"}, "userDefineArray": {"type": "array", "description": "用户自定义数据表", "items": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "columnCode": {"type": "string", "description": "字段编码"}, "columnLength": {"type": "string", "description": "字段长度"}, "columnName": {"type": "string", "description": "列名"}, "columnType": {"type": "string", "description": "字段类型"}, "columnValue": {"type": "string", "description": "取值范围"}, "option": {"type": "string", "description": "操作类型"}}}}, "voucherType": {"type": "string", "description": "开票类型"}, "writtenLanguage": {"type": "string", "description": "书写语言"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}}}}}, "/cif/nfin/client/public/password/maint": {"post": {"tags": null, "summary": "客户密码维护", "description": "对客户密码进行新增,更新,删除,校验,重置", "operationId": "ICore12009108", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "password": {"type": "string", "description": "密码"}, "passwordOld": {"type": "string", "description": "旧密码"}, "passwordOperateType": {"type": "string", "description": "账户密码操作类型"}, "passwordType": {"type": "string", "description": "密码类型编号"}, "selectType": {"type": "string", "description": "查询类型"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"isExistPassword": {"type": "string", "description": "是否存在密码"}, "passwordNew": {"type": "string", "description": "新密码"}}}}}}}, "/cif/nfin/client/public/synchrodata": {"post": {"tags": null, "summary": "对公客户信息同步到核心-外围", "description": "对公客户信息同步到核心，专供其他系统使用，将外系统客户信息的维护更新同步到核心中", "operationId": "ICore12009333", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"acctVerifyState": {"type": "string", "description": "客户核实状态"}, "branchInnerFlag": {"type": "string", "description": "是否机构内客户标志"}, "categoryType": {"type": "string", "description": "客户细分类型"}, "city": {"type": "string", "description": "城市"}, "cityDist": {"type": "string", "description": "区/县"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientStatus": {"type": "string", "description": "客户状态"}, "clientType": {"type": "string", "description": "客户类型"}, "company": {"type": "string", "description": "法人"}, "countryLoc": {"type": "string", "description": "国籍"}, "ctrlBranch": {"type": "string", "description": "控制分行"}, "documentInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "issCountry": {"type": "string", "description": "发证国家"}, "issDate": {"type": "string", "description": "签发日期"}, "maturityDate": {"type": "string", "description": "到期日期"}}}}, "inlandOffshore": {"type": "string", "description": "境内境外标志"}, "internalIndFlag": {"type": "string", "description": "内部客户标志"}, "legalRep": {"type": "string", "description": "法定代表人名称"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "repDocumentExpiryDate": {"type": "string", "description": "法定代表人证件到期日"}, "repDocumentId": {"type": "string", "description": "法定代表人身份证件号码"}, "repDocumentType": {"type": "string", "description": "法定代表人身份证件类型"}, "repNation": {"type": "string", "description": "法定代表人国籍"}, "state": {"type": "string", "description": "省别代码"}, "taxResidentFlag": {"type": "string", "description": "税收居民标识"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/relation/maint": {"post": {"tags": null, "summary": "客户关系维护", "description": "对客户关系进行新增修改删除，客户关系包括关系人或反向关系", "operationId": "ICore12009111", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientRelationArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"approveDate": {"type": "string", "description": "批准日期"}, "assessNetval": {"type": "number", "description": "评定净值", "format": "double"}, "assessNetvalCcy": {"type": "string", "description": "评定净值币种"}, "clientA": {"type": "string", "description": "客户A"}, "clientB": {"type": "string", "description": "客户B"}, "clientNo": {"type": "string", "description": "客户号"}, "clientShort": {"type": "string", "description": "客户简称"}, "declareDate": {"type": "string", "description": "宣告日期"}, "equityPercent": {"type": "number", "description": "企业占股比例", "format": "double"}, "evalNetval": {"type": "number", "description": "估价净值", "format": "double"}, "evalNetvalCcy": {"type": "string", "description": "估价净值币种"}, "inverseRelaDesc": {"type": "string", "description": "反向关系描述"}, "inverseRelaType": {"type": "string", "description": "反向关系类型"}, "noticeNetval": {"type": "number", "description": "公告净值", "format": "double"}, "noticeNetvalCcy": {"type": "string", "description": "公告净值币种"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "relaBirthDate": {"type": "string", "description": "关联方出生日期"}, "relaCountry": {"type": "string", "description": "关联方国籍"}, "relaCreditCode": {"type": "string", "description": "关联方信用代码"}, "relaDocumentId": {"type": "string", "description": "关联方证件号"}, "relaDocumentType": {"type": "string", "description": "关联方证件类型"}, "relaEducation": {"type": "string", "description": "关联方学历"}, "relaEmployerAdr": {"type": "string", "description": "关联方单位地址"}, "relaEmployerNm": {"type": "string", "description": "关联方单位名称"}, "relaEmployerTel": {"type": "string", "description": "关联方单位电话"}, "relaLoanCard": {"type": "string", "description": "关联方贷款编号"}, "relaOrgCode": {"type": "string", "description": "关联方组织机构代码"}, "relaRegCode": {"type": "string", "description": "关联方登记注册代码"}, "relaSex": {"type": "string", "description": "关联方性别"}, "relaTel": {"type": "string", "description": "关联方联系电话"}, "relaType": {"type": "string", "description": "关联方类型"}, "relationType": {"type": "string", "description": "关系类型"}}}}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/repeatphones/maint": {"post": {"tags": null, "summary": "手机号重复", "description": "对手机号重复的客户号进行黑名单核实信息登记，目前标准版本暂未使用", "operationId": "ICore12009119", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"addFlag": {"type": "string", "description": "添加标识"}, "clientListArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "clientShort": {"type": "string", "description": "客户简称"}}}}, "clientNo": {"type": "string", "description": "客户号"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/client/restraint/maint": {"post": {"tags": null, "summary": "客户级限制", "description": "对客户进行限制信息维护，包含新增限制或解除限制，也可以对限制信息进行修改", "operationId": "ICore12000122", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "endDate": {"type": "string", "description": "结束日期"}, "maintainType": {"type": "string", "description": "维护方式"}, "narrative": {"type": "string", "description": "摘要"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}, "resSeqNo": {"type": "string", "description": "限制编号"}, "restraintType": {"type": "string", "description": "限制类型"}, "startDate": {"type": "string", "description": "开始日期"}, "term": {"type": "string", "description": "存期期限"}, "termType": {"type": "string", "description": "期限类型"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object", "properties": {"resSeqNo": {"type": "string", "description": "限制编号"}}}}}}}, "/cif/nfin/client/sharedphones/maint": {"post": {"tags": null, "summary": "客户共用电话登记维护", "description": "对某个电话是多个客户使用的情况进行登记，目前标准版本暂未使用，这个登记簿也应该是日间检查，日终批处理查找全库生成", "operationId": "ICore12009117", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"sharedClientNo": {"type": "string", "description": "共用电话客户号"}}}}, "clientNo": {"type": "string", "description": "客户号"}, "clientShort": {"type": "string", "description": "客户简称"}, "contactTel": {"type": "string", "description": "联系电话  "}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/rundate/change": {"post": {"tags": null, "summary": "CIF接收OB外部营业日期", "description": "ob系统调用此接口,向cif推送新的营业日期", "operationId": "ICore12008902", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"nextRunDate": {"type": "string", "description": "下一运行日"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/cif/nfin/user/access": {"post": {"tags": null, "summary": "柜员权限维护", "description": "用于维护柜员权限表", "operationId": "ICore12003001", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"amendOperateType": {"type": "string", "description": "变更操作类型"}, "businessList": {"type": "string", "description": "行业代码组"}, "clientTypeList": {"type": "string", "description": "客户类型组"}, "occupationCodeList": {"type": "string", "description": "职业组"}, "userId": {"type": "string", "description": "交易柜员"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "object"}}}}}, "/pf/inq/param/query/prodtype": {"post": {"tags": null, "summary": "根据客户类型查询产品类型", "description": "", "operationId": "ICore14004005", "parameters": [{"name": "body", "in": "body", "description": "Request body", "required": true, "type": "", "schema": {"type": "object", "properties": {"clientType": {"type": "string", "description": "客户类型"}, "sourceModule": {"type": "string", "description": "源模块"}}}}], "responses": {"200": {"description": "Success", "schema": {"type": "array", "items": {"type": "object"}}}}}}}, "definitions": {"ResponseCRS信息查询": {"type": "object", "properties": {"annualThreshold": {"type": "string", "description": "年度重分类阈值"}, "area": {"type": "string", "description": "国家地区"}, "balanceThreshold": {"type": "string", "description": "补救平衡阈值"}, "ccy": {"type": "string", "description": "币种"}, "city": {"type": "string", "description": "城市"}, "clientNo": {"type": "string", "description": "客户号"}, "einNo": {"type": "string", "description": "系统编号"}, "homeAddr": {"type": "string", "description": "居住地址"}, "jsonBranch": {"type": "string", "description": "机构信息大字段"}, "jsonControl": {"type": "string", "description": "实际控制人信息大字段"}, "jsonPeople": {"type": "string", "description": "个人信息大字段"}, "province": {"type": "string", "description": "省"}, "taxCountry": {"type": "string", "description": "税收居民国"}, "taxGovernment": {"type": "string", "description": "税务分类制度"}, "taxResidentFlag": {"type": "string", "description": "税收居民标识"}, "taxStatus": {"type": "string", "description": "税务分类状况"}}}, "ResponseFATCA信息查询": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "facilityJson": {"type": "string", "description": "海外账户税收合规法案信息大字段"}, "residentArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"noTaxpayer": {"type": "string", "description": "无纳税人识别号"}, "remark1": {"type": "string", "description": "备注1"}, "remark2": {"type": "string", "description": "备注2"}, "taxpayerId": {"type": "string", "description": "纳税人识别号"}}}}}}, "Response业务信息修改记录查询": {"type": "object", "properties": {"amendArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"afterVal": {"type": "string", "description": "变更后值"}, "amendDate": {"type": "string", "description": "变更日期"}, "amendItem": {"type": "string", "description": "修改项"}, "amendSeqNo": {"type": "string", "description": "变更序号"}, "amendType": {"type": "string", "description": "账户变更类型"}, "apprFlag": {"type": "string", "description": "复核标志"}, "apprUserId": {"type": "string", "description": "复核柜员"}, "approvalDate": {"type": "string", "description": "复核日期"}, "beforeVal": {"type": "string", "description": "变更前值"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "narrative": {"type": "string", "description": "摘要"}, "reference": {"type": "string", "description": "交易参考号"}, "sourceType": {"type": "string", "description": "渠道类型"}, "tranBranch": {"type": "string", "description": "交易机构"}, "tranTimestamp": {"type": "string", "description": "交易时间戳"}, "userId": {"type": "string", "description": "交易柜员"}}}}}}, "Response参数统一查询": {"type": "object", "properties": {"resultArray": {"type": "array", "description": "数组", "items": {"type": "object"}}}}, "Response客户关系查询": {"type": "object", "properties": {"clientArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"clientA": {"type": "string", "description": "客户A"}, "clientNo": {"type": "string", "description": "客户号"}, "clientShort": {"type": "string", "description": "客户简称"}, "clientShortA": {"type": "string", "description": "关联方简称"}, "relationType": {"type": "string", "description": "关系类型"}}}}, "relationArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"approvalDate": {"type": "string", "description": "复核日期"}, "assessNetval": {"type": "number", "description": "评定净值", "format": "double"}, "assessNetvalCcy": {"type": "string", "description": "评定净值币种"}, "clientA": {"type": "string", "description": "客户A"}, "clientNo": {"type": "string", "description": "客户号"}, "clientShort": {"type": "string", "description": "客户简称"}, "clientShortA": {"type": "string", "description": "关联方简称"}, "closedDate": {"type": "string", "description": "关闭日期"}, "createDate": {"type": "string", "description": "创建日期"}, "creationUserId": {"type": "string", "description": "创建柜员"}, "declareDate": {"type": "string", "description": "宣告日期"}, "equityPercent": {"type": "number", "description": "企业占股比例", "format": "double"}, "evalNetval": {"type": "number", "description": "估价净值", "format": "double"}, "evalNetvalCcy": {"type": "string", "description": "估价净值币种"}, "inverseRelaDesc": {"type": "string", "description": "反向关系描述"}, "inverseRelaType": {"type": "string", "description": "反向关系类型"}, "lastChangeDate": {"type": "string", "description": "最后修改日期"}, "lastChangeUserId": {"type": "string", "description": "最后修改柜员"}, "noticeNetval": {"type": "number", "description": "公告净值", "format": "double"}, "noticeNetvalCcy": {"type": "string", "description": "公告净值币种"}, "relaDocumentId": {"type": "string", "description": "关联方证件号"}, "relaDocumentType": {"type": "string", "description": "关联方证件类型"}, "relationType": {"type": "string", "description": "关系类型"}, "status": {"type": "string", "description": "状态"}}}}}}, "Response客户合并": {"type": "object", "properties": {"mergeNo": {"type": "string", "description": "合并编号"}}}, "Response客户合并查询": {"type": "object", "properties": {"failureNumber": {"type": "string", "description": "失败数量"}, "mergeArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"changeDate": {"type": "string", "description": "交换日期"}, "company": {"type": "string", "description": "法人"}, "errorDesc": {"type": "string", "description": "错误描述"}, "mergeNo": {"type": "string", "description": "合并编号"}, "newClientNo": {"type": "string", "description": "新客户号"}, "okFlag": {"type": "string", "description": "是否已完成"}, "oldClientNo": {"type": "string", "description": "原客户号"}, "sourceModule": {"type": "string", "description": "源模块"}, "tableName": {"type": "string", "description": "表名"}}}}, "successNumber": {"type": "string", "description": "处理成功的数量"}}}, "Response客户基本信息查询": {"type": "object", "properties": {"accountOpenPurpose": {"type": "string", "description": "开户原因"}, "acctExec": {"type": "string", "description": "客户经理"}, "acctExecName": {"type": "string", "description": "客户经理姓名"}, "address": {"type": "string", "description": "地址"}, "amlLastReviewDate": {"type": "string", "description": "AML最后核查时间"}, "amlNextReviewDate": {"type": "string", "description": "AML下次核查时间"}, "birthDate": {"type": "string", "description": "出生日期"}, "blacklistIndFlag": {"type": "string", "description": "是否黑名单客户"}, "categoryType": {"type": "string", "description": "客户细分类型"}, "childrenFlag": {"type": "string", "description": "未成年标志"}, "classLevel": {"type": "string", "description": "综合评级"}, "classLevelDate": {"type": "string", "description": "评级日期"}, "clientAlias": {"type": "string", "description": "别名"}, "clientCity": {"type": "string", "description": "城市代码"}, "clientGroup": {"type": "string", "description": "客户群组\n"}, "clientIndicator": {"type": "string", "description": "客户标识"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientPoint": {"type": "string", "description": "客户积分"}, "clientShort": {"type": "string", "description": "客户简称"}, "clientTranStatus": {"type": "string", "description": "客户交易状态"}, "clientType": {"type": "string", "description": "客户类型"}, "company": {"type": "string", "description": "法人"}, "contactArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "lastChangeDate": {"type": "string", "description": "最后修改日期"}, "lastChangeUserId": {"type": "string", "description": "最后修改柜员"}, "linkmanName": {"type": "string", "description": "联系人名称"}, "linkmanType": {"type": "string", "description": "联系人类型"}, "phoneNo1": {"type": "string", "description": "电话号码 1"}, "phoneNo2": {"type": "string", "description": "联系人电话2"}}}}, "contactInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"addrMode": {"type": "string", "description": "地址模式"}, "address": {"type": "string", "description": "地址"}, "address1": {"type": "string", "description": "地址1"}, "city": {"type": "string", "description": "城市"}, "cityDist": {"type": "string", "description": "区/县"}, "cityTel": {"type": "string", "description": "电话区号"}, "contactTel": {"type": "string", "description": "联系电话  "}, "contactType": {"type": "string", "description": "联系类型"}, "country": {"type": "string", "description": "国家"}, "countryTel": {"type": "string", "description": "国家电话区号"}, "email": {"type": "string", "description": "电子邮件"}, "mobilePhone": {"type": "string", "description": "移动电话"}, "postalCode": {"type": "string", "description": "邮政编码"}, "prefFlag": {"type": "string", "description": "首选标志"}, "rt": {"type": "string", "description": "地区编号"}, "rw": {"type": "string", "description": "地区编码"}, "state": {"type": "string", "description": "省别代码"}, "subDistrict": {"type": "string", "description": "子区"}}}}, "contactTelUpdateDate": {"type": "string", "description": "联系电话更新日期"}, "contributeDegree": {"type": "string", "description": "贡献度"}, "corporation": {"type": "string", "description": "是否为企业"}, "countryCitizen": {"type": "string", "description": "居住国家"}, "countryLoc": {"type": "string", "description": "国籍"}, "crRating": {"type": "string", "description": "客户信用等级"}, "ctrlBranch": {"type": "string", "description": "控制分行"}, "debtorCategory": {"type": "string", "description": "借款人类别"}, "district": {"type": "string", "description": "区号"}, "documentId": {"type": "string", "description": "证件号码"}, "documentInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "issCountry": {"type": "string", "description": "发证国家"}, "issDate": {"type": "string", "description": "签发日期"}, "issPlace": {"type": "string", "description": "签发地"}, "issueBranch": {"type": "string", "description": "签发机构"}, "maturityDate": {"type": "string", "description": "到期日期"}, "prefFlag": {"type": "string", "description": "首选标志"}, "visaType": {"type": "string", "description": "签证类型"}}}}, "documentType": {"type": "string", "description": "证件类型"}, "education": {"type": "string", "description": "教育程度编号"}, "enClientName": {"type": "string", "description": "客户英文名称"}, "industry": {"type": "string", "description": "通用行业代码"}, "industryLevel": {"type": "string", "description": "通用行业层级"}, "infoLack": {"type": "string", "description": "资料不全标志"}, "inlandOffshore": {"type": "string", "description": "境内境外标志"}, "internalIndFlag": {"type": "string", "description": "内部客户标志"}, "isIndividual": {"type": "string", "description": "个体客户标志"}, "kycFlag": {"type": "string", "description": "kyc标志"}, "mothersName": {"type": "string", "description": "母亲姓名"}, "nation": {"type": "string", "description": "民族"}, "npwpFlag": {"type": "string", "description": "税务标志"}, "npwpNumber": {"type": "string", "description": "税务编号"}, "occupationCode": {"type": "string", "description": "职业"}, "pep": {"type": "string", "description": "AML政要级别"}, "prenupialAgreement": {"type": "string", "description": "婚前协议"}, "printLanguage": {"type": "string", "description": "打印语言"}, "race": {"type": "string", "description": "种族"}, "relationshipToBank": {"type": "string", "description": "是否关联"}, "resFlag": {"type": "string", "description": "冻结标志"}, "riskScore": {"type": "string", "description": "AML风险评分"}, "sex": {"type": "string", "description": "性别"}, "sourceOfIncome": {"type": "string", "description": "收入来源"}, "spouseDateOfBirth": {"type": "string", "description": "配偶生日"}, "spouseDocumentId": {"type": "string", "description": "配偶证件号码"}, "spouseName": {"type": "string", "description": "配偶姓名"}, "spouseRealEstateContract": {"type": "string", "description": "配偶房产合同"}, "state": {"type": "string", "description": "省别代码"}, "taxFlag": {"type": "string", "description": "是否税信息"}, "tempClient": {"type": "string", "description": "临时客户标志"}}}, "Response客户密码维护": {"type": "object", "properties": {"isExistPassword": {"type": "string", "description": "是否存在密码"}, "passwordNew": {"type": "string", "description": "新密码"}}}, "Response客户批量开立查询": {"type": "object", "properties": {"mergeArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"chClientName": {"type": "string", "description": "客户中文名称"}, "clientNo": {"type": "string", "description": "客户号"}, "counter": {"type": "string", "description": "序号 "}, "dealStatus": {"type": "string", "description": "处理状态"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "failureReason": {"type": "string", "description": "失败原因"}, "sex": {"type": "string", "description": "性别"}}}}, "succNum": {"type": "string", "description": "成功数量"}, "totalNum": {"type": "string", "description": "总数量"}}}, "Response客户渠道限制": {"type": "object", "properties": {"controlSeqNo": {"type": "string", "description": "控制编号"}}}, "Response客户渠道限制查询": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "ctrlArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"authUserId": {"type": "string", "description": "授权柜员"}, "controlSeqNo": {"type": "string", "description": "控制编号"}, "controlStatus": {"type": "string", "description": "控制状态"}, "controlType": {"type": "string", "description": "控制类型"}, "endDate": {"type": "string", "description": "结束日期"}, "lastChangeDate": {"type": "string", "description": "最后修改日期"}, "lastUserId": {"type": "string", "description": "上一柜员ID"}, "limitLevel": {"type": "string", "description": "限制级别"}, "narrative": {"type": "string", "description": "摘要"}, "startDate": {"type": "string", "description": "开始日期"}}}}}}, "Response客户相似信息查询": {"type": "object", "properties": {"resultArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "phone": {"type": "string", "description": "手机号"}}}}}}, "Response客户级限制": {"type": "object", "properties": {"resSeqNo": {"type": "string", "description": "限制编号"}}}, "Response客户组成员信息查询": {"type": "object", "properties": {"clientArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"chClientName": {"type": "string", "description": "客户中文名称"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientTranStatus": {"type": "string", "description": "客户交易状态"}, "ctrlBranch": {"type": "string", "description": "控制分行"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}}}}}}, "Response客户经理信息查询": {"type": "object", "properties": {"execArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"acctExec": {"type": "string", "description": "客户经理"}, "acctExecName": {"type": "string", "description": "客户经理姓名"}, "acctExecStatus": {"type": "string", "description": "客户经理状态"}, "acctExecType": {"type": "string", "description": "客户经理类型"}, "branch": {"type": "string", "description": "所属机构号"}, "collatMgrInd": {"type": "string", "description": "是否担保经理"}, "contactTel": {"type": "string", "description": "联系电话  "}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "issCountry": {"type": "string", "description": "发证国家"}, "manager": {"type": "string", "description": "主管经理"}, "profitCenter": {"type": "string", "description": "利润中心 "}}}}}}, "Response客户经理维护": {"type": "object", "properties": {"acctExec": {"type": "string", "description": "客户经理"}}}, "Response客户证件信息查询": {"type": "object", "properties": {"clientDocumentArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"address": {"type": "string", "description": "地址"}, "clientNo": {"type": "string", "description": "客户号"}, "clientShort": {"type": "string", "description": "客户简称"}, "clientType": {"type": "string", "description": "客户类型"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "issCountry": {"type": "string", "description": "发证国家"}, "issDate": {"type": "string", "description": "签发日期"}, "issPlace": {"type": "string", "description": "签发地"}, "issueBranch": {"type": "string", "description": "签发机构"}, "maturityDate": {"type": "string", "description": "到期日期"}}}}}}, "Response客户证件到期信息查询": {"type": "object", "properties": {"documentInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"branch": {"type": "string", "description": "所属机构号"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "date": {"type": "string", "description": "日期"}, "documentId": {"type": "string", "description": "证件号码"}, "documentIssDate": {"type": "string", "description": "证件签发日期"}, "documentType": {"type": "string", "description": "证件类型"}, "maturityDate": {"type": "string", "description": "到期日期"}}}}}}, "Response客户证件批量查询": {"type": "object", "properties": {"clientArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"chClientName": {"type": "string", "description": "客户中文名称"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientShort": {"type": "string", "description": "客户简称"}, "clientType": {"type": "string", "description": "客户类型"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "enClientName": {"type": "string", "description": "客户英文名称"}, "isIndividual": {"type": "string", "description": "个体客户标志"}}}}}}, "Response客户详细信息查询": {"type": "object", "properties": {"accountOpenPurpose": {"type": "string", "description": "开户原因"}, "acctExec": {"type": "string", "description": "客户经理"}, "acctExecCode": {"type": "string", "description": "客户经理代码"}, "address": {"type": "string", "description": "地址"}, "amlCheckFlag": {"type": "string", "description": "反洗钱检查标志"}, "amlLastReviewDate": {"type": "string", "description": "AML最后核查时间"}, "amlNextReviewDate": {"type": "string", "description": "AML下次核查时间"}, "areaName": {"type": "string", "description": "区域名称"}, "authCapital": {"type": "number", "description": "注册资本", "format": "double"}, "bankCode": {"type": "string", "description": "银行代码"}, "basicAcctNo": {"type": "string", "description": "基本账号"}, "basicAcctOpenat": {"type": "string", "description": "基本账户开户行"}, "birthDate": {"type": "string", "description": "出生日期"}, "blacklistIndFlag": {"type": "string", "description": "是否黑名单客户"}, "blankShare": {"type": "string", "description": "不记名股票"}, "borrowerGrade": {"type": "string", "description": "借款人等级"}, "business": {"type": "string", "description": "行业代码"}, "businessPartner": {"type": "string", "description": "业务合作伙伴"}, "businessScope": {"type": "string", "description": "经营范围"}, "capitalCcy": {"type": "string", "description": "注册资本币种"}, "categoryType": {"type": "string", "description": "客户细分类型"}, "centralBankRef": {"type": "string", "description": "中央银行"}, "cessation": {"type": "string", "description": "终止类型"}, "cessationDate": {"type": "string", "description": "终止日期"}, "chGivenName": {"type": "string", "description": "中文名"}, "chSurname": {"type": "string", "description": "中文姓"}, "checkDate": {"type": "string", "description": "检查日期"}, "childNum": {"type": "string", "description": "孩子人数"}, "cifBenefitOwnerInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"address": {"type": "string", "description": "地址"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "isSpecialPeople": {"type": "string", "description": "是否特定自然人"}, "maturityDate": {"type": "string", "description": "到期日期"}, "ownerName": {"type": "string", "description": "所有人姓名"}}}}, "cifClientNraDetailArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"nraCountry": {"type": "string", "description": "非居民居民国（地区）"}, "nraIdTax": {"type": "string", "description": "非居民纳税人识别号"}, "nraIdTaxDetres": {"type": "string", "description": "未取得纳税人识别号具体原因"}, "nraIdTaxRes": {"type": "string", "description": "非居民未提供纳税人识别号原因"}}}}, "cifClientSuperCropArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"superCropAcctCheckNo": {"type": "string", "description": "上级法人或主管单位基本存款账户开户许可证核准号"}, "superCropDocExpiryDate": {"type": "string", "description": "上级法人或主管单位证件有效期"}, "superCropName": {"type": "string", "description": "上级法人或主管单位名称"}, "superCropOrgCode": {"type": "string", "description": "上级法人或主管单位组织机构代码"}, "superDocExpiryDate": {"type": "string", "description": "上级法人或单位负责人证件有效期"}, "superDocumentId": {"type": "string", "description": "上级法人或单位负责人证件号码"}, "superDocumentType": {"type": "string", "description": "上级法人或单位负责人证件种类"}, "superName": {"type": "string", "description": "上级法人或单位负责人姓名"}}}}, "cifContactArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "linkmanName": {"type": "string", "description": "联系人名称"}, "linkmanType": {"type": "string", "description": "联系人类型"}, "phoneNo1": {"type": "string", "description": "电话号码 1"}, "phoneNo2": {"type": "string", "description": "联系人电话2"}}}}, "cifCropCompnayInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"companyGovernorName": {"type": "string", "description": "法人/财务主管名称 "}, "compnayGovernor": {"type": "string", "description": "法人/财务主管 "}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "maturityDate": {"type": "string", "description": "到期日期"}}}}, "cifRealControlInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"conDocumentId": {"type": "string", "description": "控制人证件号码 "}, "conDocumentType": {"type": "string", "description": "控制人证件类型 "}, "conName": {"type": "string", "description": "控制人姓名 "}, "maturityDate": {"type": "string", "description": "到期日期"}, "optionCli": {"type": "string", "description": "对客户信息操作类型"}}}}, "cifVerFlag": {"type": "string", "description": "客户级核实标志"}, "class1": {"type": "string", "description": "分类1"}, "class2": {"type": "string", "description": "分类2"}, "class3": {"type": "string", "description": "分类3"}, "class4": {"type": "string", "description": "分类4"}, "class5": {"type": "string", "description": "分类5"}, "classLevel": {"type": "string", "description": "综合评级"}, "classLevelDate": {"type": "string", "description": "评级日期"}, "client": {"type": "string", "description": "客户"}, "clientAlias": {"type": "string", "description": "别名"}, "clientCity": {"type": "string", "description": "城市代码"}, "clientClass": {"type": "string", "description": "客户类别"}, "clientFullName": {"type": "string", "description": "客户全名称"}, "clientGroup": {"type": "string", "description": "客户群组\n"}, "clientGrp": {"type": "string", "description": "客户组"}, "clientIndicator": {"type": "string", "description": "客户标识"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientPoint": {"type": "string", "description": "客户积分"}, "clientShort": {"type": "string", "description": "客户简称"}, "clientSpread": {"type": "number", "description": "客户浮动", "format": "double"}, "clientTranStatus": {"type": "string", "description": "客户交易状态"}, "clientType": {"type": "string", "description": "客户类型"}, "closedDate": {"type": "string", "description": "关闭日期"}, "commissionClientName": {"type": "string", "description": "代办人名称"}, "commissionDocumentId": {"type": "string", "description": "代办人证件号码"}, "commissionDocumentType": {"type": "string", "description": "代办人证件类型"}, "commissionExpireDate": {"type": "string", "description": "代办人证件到期日"}, "company": {"type": "string", "description": "法人"}, "companyOwnIndividual": {"type": "string", "description": "由个人全资拥有的公司"}, "companySecretaryFlag": {"type": "string", "description": "是否指定公司秘书"}, "conLendingParty": {"type": "string", "description": "关联贷款方"}, "conNameCh": {"type": "string", "description": "控制人姓名（中文） "}, "conNameEn": {"type": "string", "description": "控制人姓名（英文） "}, "conOfCust": {"type": "string", "description": "客户集中度"}, "conOfSupple": {"type": "string", "description": "供应商集中度"}, "contactInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"address": {"type": "string", "description": "地址"}, "address1": {"type": "string", "description": "地址1"}, "bicCode": {"type": "string", "description": "BIC代码"}, "city": {"type": "string", "description": "城市"}, "cityDist": {"type": "string", "description": "区/县"}, "cityTel": {"type": "string", "description": "电话区号"}, "contactTel": {"type": "string", "description": "联系电话  "}, "contactType": {"type": "string", "description": "联系类型"}, "country": {"type": "string", "description": "国家"}, "countryTel": {"type": "string", "description": "国家电话区号"}, "email": {"type": "string", "description": "电子邮件"}, "linkman": {"type": "string", "description": "对账联系人"}, "mobilePhone": {"type": "string", "description": "移动电话"}, "postalCode": {"type": "string", "description": "邮政编码"}, "prefFlag": {"type": "string", "description": "首选标志"}, "route": {"type": "string", "description": "联系方式类型"}, "rt": {"type": "string", "description": "地区编号"}, "rw": {"type": "string", "description": "地区编码"}, "salutation": {"type": "string", "description": "称呼"}, "state": {"type": "string", "description": "省别代码"}, "subDistrict": {"type": "string", "description": "子区"}}}}, "contraType": {"type": "string", "description": "对手方类型"}, "contributeDegree": {"type": "string", "description": "贡献度"}, "controlContactInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"contactAddressType": {"type": "string", "description": "联系地址类型 "}, "controlCity": {"type": "string", "description": "控制人所在城市"}, "controlContactTel": {"type": "string", "description": "控制人联系电话  "}, "controlCountry": {"type": "string", "description": "控制人国家"}, "controlState": {"type": "string", "description": "控制人省、州"}}}}, "controlTaxBirthday": {"type": "string", "description": "控制人出生日期"}, "controlTaxCountry": {"type": "string", "description": "控制人税收居民国"}, "controlTaxFlag": {"type": "string", "description": "控制人税收居民标识"}, "controlTaxName": {"type": "string", "description": "控制人客户姓名"}, "controlTaxpayerId": {"type": "string", "description": "控制人纳税人识别号"}, "controlUnprovideIdReason": {"type": "string", "description": "控制人未提供识别号原因"}, "coreMarketFlag": {"type": "string", "description": "核心市场参与者"}, "corpFlag": {"type": "string", "description": "是否小微企业"}, "corpPlan": {"type": "string", "description": "公司计划  "}, "corpSize": {"type": "string", "description": "企业规模 "}, "corporateShare": {"type": "string", "description": "拥有海外个人公司股东的本地公司"}, "countryCitizen": {"type": "string", "description": "居住国家"}, "countryCode": {"type": "string", "description": "国家代码"}, "countryLoc": {"type": "string", "description": "国籍"}, "countryRisk": {"type": "string", "description": "风险控制国家"}, "crRating": {"type": "string", "description": "客户信用等级"}, "createDate": {"type": "string", "description": "创建日期"}, "creationUserId": {"type": "string", "description": "创建柜员"}, "ctrlBranch": {"type": "string", "description": "控制分行"}, "customerDue": {"type": "string", "description": "经批准的中介机构介绍"}, "debtorCategory": {"type": "string", "description": "借款人类别"}, "dependentNum": {"type": "string", "description": "供养人数"}, "directUnchain": {"type": "string", "description": "所有权链中提名董事"}, "directorInd": {"type": "string", "description": "是否指定银行负责人"}, "documentId": {"type": "string", "description": "证件号码"}, "documentInfoArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"distCode": {"type": "string", "description": "地区代码"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "inspectDate": {"type": "string", "description": "上次核查日期"}, "issCity": {"type": "string", "description": "签发城市"}, "issCountry": {"type": "string", "description": "发证国家"}, "issDate": {"type": "string", "description": "签发日期"}, "issPlace": {"type": "string", "description": "签发地"}, "issState": {"type": "string", "description": "签发省、州"}, "issueBranch": {"type": "string", "description": "签发机构"}, "maturityDate": {"type": "string", "description": "到期日期"}, "otherDocument": {"type": "string", "description": "其他证件名称"}, "passportType": {"type": "string", "description": "护照类型"}, "prefFlag": {"type": "string", "description": "首选标志"}, "visaType": {"type": "string", "description": "签证类型"}}}}, "documentType": {"type": "string", "description": "证件类型"}, "econDist": {"type": "string", "description": "经济特区"}, "econType": {"type": "string", "description": "经济类型"}, "education": {"type": "string", "description": "教育程度编号"}, "empNum": {"type": "string", "description": "员工数"}, "employerFlag": {"type": "string", "description": "员工标志"}, "employerIndustry": {"type": "string", "description": "雇主所在行业"}, "employerName": {"type": "string", "description": "工作单位"}, "employmentStartDate": {"type": "string", "description": "雇佣开始日期"}, "enClientName": {"type": "string", "description": "客户英文名称"}, "exposureCap": {"type": "string", "description": "风险控制标志"}, "extRatingMod": {"type": "string", "description": "外部评级穆迪"}, "extRatingNormal": {"type": "string", "description": "外部评级标普"}, "finAppCode": {"type": "string", "description": "金融机构许可证号"}, "firstName": {"type": "string", "description": "名"}, "fitch": {"type": "string", "description": "Fitch等级"}, "forInUnchain": {"type": "string", "description": "所有权链中的基金会"}, "foreRemitCerAvai": {"type": "string", "description": "外汇证有效期  "}, "foreignAppNo": {"type": "string", "description": "外商投资批准证书号"}, "formOfCompany": {"type": "string", "description": "公司形式"}, "ftfFlag": {"type": "string", "description": "个人非居民标志"}, "fxIssOrgan": {"type": "string", "description": "外汇等级证号"}, "fxIssPlace": {"type": "string", "description": "外汇登记证签发地"}, "fxRegisterId": {"type": "string", "description": "外汇登记证"}, "givenName": {"type": "string", "description": "英文名"}, "governmentRe": {"type": "string", "description": "政府关联标志"}, "groupName": {"type": "string", "description": "账户组名称"}, "guarantyAssetsClass": {"type": "string", "description": "担保人资产等级"}, "higherOrgan": {"type": "string", "description": "主管单位"}, "hobby": {"type": "string", "description": "兴趣爱好"}, "identifyFlag": {"type": "string", "description": "是否免于识别"}, "incProofDate": {"type": "string", "description": "收入验证日期"}, "incProofInd": {"type": "string", "description": "收入验证标识"}, "incProofUserId": {"type": "string", "description": "收入验证人"}, "incorDate": {"type": "string", "description": "公司成立日期"}, "industry": {"type": "string", "description": "通用行业代码"}, "infoLack": {"type": "string", "description": "资料不全标志"}, "inlandOffshore": {"type": "string", "description": "境内境外标志"}, "innerRaintg": {"type": "string", "description": "内部评级"}, "inpExpNo": {"type": "string", "description": "进出口业务经营资格编号"}, "internalIndFlag": {"type": "string", "description": "内部客户标志"}, "intraGroupFlag": {"type": "string", "description": "跨群组标志"}, "investServiceFlag": {"type": "string", "description": "投资服务同意书标志"}, "investor": {"type": "string", "description": "投资人"}, "investorCountry": {"type": "string", "description": "外方投资人国别"}, "isFormal": {"type": "string", "description": "弱实名客户标志"}, "isSave": {"type": "string", "description": "留存标志"}, "issCountry": {"type": "string", "description": "发证国家"}, "kycFlag": {"type": "string", "description": "kyc标志"}, "lastChangeDate": {"type": "string", "description": "最后修改日期"}, "lastChangeUserId": {"type": "string", "description": "最后修改柜员"}, "lastName": {"type": "string", "description": "姓"}, "legalRep": {"type": "string", "description": "法定代表人名称"}, "lendingOfficerInd": {"type": "string", "description": "是否指定贷款副负责人"}, "liaIndicatorFlag": {"type": "string", "description": "诉讼标志"}, "loanCardId": {"type": "string", "description": "该企业贷款使用的贷款卡编码"}, "loanGrade": {"type": "string", "description": "贷方级别"}, "localTaxFileNo": {"type": "string", "description": "地方税务证号"}, "maidenName": {"type": "string", "description": "婚前名"}, "manageContent": {"type": "string", "description": "监管内容"}, "maritalStatus": {"type": "string", "description": "婚姻状况"}, "marketParticipant": {"type": "string", "description": "市场参与者"}, "maxDegree": {"type": "string", "description": "最高学位"}, "midName": {"type": "string", "description": "中间名"}, "minorityInterest": {"type": "string", "description": "是否最小控股,不能超过10%的股权"}, "modeOfInformation": {"type": "string", "description": "经营模式"}, "monMortgage": {"type": "number", "description": "月抵押付给金额", "format": "double"}, "monRental": {"type": "number", "description": "月租金", "format": "double"}, "monSalary": {"type": "number", "description": "月薪", "format": "double"}, "moodyRate": {"type": "number", "description": "外部浮动利率", "format": "double"}, "mortgageCcy": {"type": "string", "description": "抵押币种"}, "mothersMaidenName": {"type": "string", "description": "母亲婚前名"}, "mothersName": {"type": "string", "description": "母亲姓名"}, "nameOfExchange": {"type": "string", "description": "交易所名称"}, "narrative": {"type": "string", "description": "摘要"}, "nation": {"type": "string", "description": "民族"}, "negNonFinFlag": {"type": "string", "description": "消极非金融标识"}, "noInterfaceClient": {"type": "string", "description": "非面对面客户"}, "npwpFlag": {"type": "string", "description": "税务标志"}, "npwpNumber": {"type": "string", "description": "税务编号"}, "nraBirthCh": {"type": "string", "description": "非居民出生地（中文）"}, "nraBirthEn": {"type": "string", "description": "非居民出生地（英文）"}, "nraBitrhday": {"type": "string", "description": "非居民出生日期"}, "nraCountry": {"type": "string", "description": "非居民居民国（地区）"}, "nraNowAddressCh": {"type": "string", "description": "非居民现居地址（中文）"}, "nraNowAddressEn": {"type": "string", "description": "非居民现居地址（英文）"}, "numberOfBranch": {"type": "string", "description": "分行数量"}, "occupationCode": {"type": "string", "description": "职业"}, "offWebsite": {"type": "string", "description": "官方网站"}, "onlineSettleFlag": {"type": "string", "description": "线上清算标志\n"}, "operateCountry": {"type": "string", "description": "运营国家"}, "organ": {"type": "string", "description": "组织机构代码"}, "originCountry": {"type": "string", "description": "注册国家"}, "original": {"type": "number", "description": "违约概率", "format": "double"}, "originalGrade": {"type": "string", "description": "撒"}, "originalStart": {"type": "number", "description": "初始违约概率", "format": "double"}, "ownershipStructure": {"type": "string", "description": "涉及三个或三个以上注册地的所有权结构"}, "paidCapitalCcy": {"type": "string", "description": "实收资本币种"}, "paidUpCapital": {"type": "number", "description": "实收资本", "format": "double"}, "payAgentFlag": {"type": "string", "description": "代付标志"}, "pcpGroupId": {"type": "string", "description": "资金池账户组ID"}, "pd": {"type": "number", "description": "违约机率", "format": "double"}, "pep": {"type": "string", "description": "AML政要级别"}, "pepCategory": {"type": "string", "description": "政治公众人物细类"}, "pepClient": {"type": "string", "description": "政治公众人物"}, "pepClose": {"type": "string", "description": "政治公众人物亲密合作方"}, "pepCountry": {"type": "string", "description": "政治公众人物所在国家"}, "pepIndFlag": {"type": "string", "description": "PEP客户标志"}, "pepInter": {"type": "string", "description": "政治公众人物所在国际组织"}, "pepRisk": {"type": "string", "description": "政治任务风险"}, "pepType": {"type": "string", "description": "政治公众人物分类"}, "phoneFax": {"type": "string", "description": "是否电话/传真指令指定客户"}, "phoneFaxAcct": {"type": "string", "description": "是否电话/传真指令指定账户客户"}, "placeOfBirth": {"type": "string", "description": "出生国"}, "placeWhereBusi": {"type": "string", "description": "业务建立地"}, "post": {"type": "string", "description": "职务"}, "prenupialAgreement": {"type": "string", "description": "婚前协议"}, "printLanguage": {"type": "string", "description": "打印语言"}, "qualification": {"type": "string", "description": "专业职称"}, "race": {"type": "string", "description": "种族"}, "raintg": {"type": "string", "description": "评级结果"}, "recAgentFlag": {"type": "string", "description": "代收标志"}, "redcrossNo": {"type": "string", "description": "红十字会员编号"}, "refIntermediaryFlag": {"type": "string", "description": "中介推崇标志"}, "relationshipToBank": {"type": "string", "description": "是否关联"}, "remark": {"type": "string", "description": "备注"}, "rentalCcy": {"type": "string", "description": "租金币种"}, "repDocumentId": {"type": "string", "description": "法定代表人身份证件号码"}, "repDocumentType": {"type": "string", "description": "法定代表人身份证件类型"}, "repExpiryDate": {"type": "string", "description": "法人代表证件到期日"}, "repIssDate": {"type": "string", "description": "法人证件签发日期"}, "repPhone": {"type": "string", "description": "法人代表手机号"}, "residentDate": {"type": "string", "description": "入住日期"}, "residentFlag": {"type": "string", "description": "居民标识"}, "residentStatus": {"type": "string", "description": "居住状态"}, "residentType": {"type": "string", "description": "居住类型"}, "riskClassifications": {"type": "string", "description": "风险分类"}, "riskRatingExpiryDate": {"type": "string", "description": "风险评级有效日期"}, "riskRatingReviewDate": {"type": "string", "description": "风险评级审查日期"}, "riskScore": {"type": "string", "description": "AML风险评分"}, "riskWeight": {"type": "string", "description": "风险权重"}, "salaryAcctBranch": {"type": "string", "description": "工资账户开户行"}, "salaryAcctNo": {"type": "string", "description": "工资账号"}, "salaryCcy": {"type": "string", "description": "薪资币种"}, "salutation": {"type": "string", "description": "称呼"}, "selfStatement": {"type": "string", "description": "取得自证声明标志"}, "sex": {"type": "string", "description": "性别"}, "sharedUnchain": {"type": "string", "description": "所有权链中的提名股东"}, "socType": {"type": "string", "description": "主权类型"}, "socialInsuNo": {"type": "string", "description": "社会保险号"}, "sourceOfIncome": {"type": "string", "description": "收入来源"}, "sourceType": {"type": "string", "description": "渠道类型"}, "spRate": {"type": "string", "description": "SP等级  "}, "specialRateFlag": {"type": "string", "description": "特殊利率"}, "spokenLanguage": {"type": "string", "description": "交流语言"}, "spouseDateOfBirth": {"type": "string", "description": "配偶生日"}, "spouseDocumentId": {"type": "string", "description": "配偶证件号码"}, "spouseName": {"type": "string", "description": "配偶姓名"}, "spouseRealEstateContract": {"type": "string", "description": "配偶房产合同"}, "startDate": {"type": "string", "description": "开始日期"}, "state": {"type": "string", "description": "省别代码"}, "subDirectorInd": {"type": "string", "description": "指定贷款负责人标志"}, "subLendingOfficerInd": {"type": "string", "description": "指定银行副负责人标志"}, "sucFlag": {"type": "string", "description": "社会统一信用代码标志"}, "surname": {"type": "string", "description": "英文姓"}, "surnameFirst": {"type": "string", "description": "是否姓在前"}, "swiftId": {"type": "string", "description": "银行国际代码"}, "swiftName": {"type": "string", "description": "贸易金融名称"}, "taxCerAvai": {"type": "string", "description": "税务证有效期"}, "taxCountry": {"type": "string", "description": "税收居民国"}, "taxFileNo": {"type": "string", "description": "国税登记号"}, "taxFlag": {"type": "string", "description": "是否税信息"}, "taxOrgType": {"type": "string", "description": "税收机构类型"}, "taxRemark": {"type": "string", "description": "税收备注"}, "taxResidentFlag": {"type": "string", "description": "税收居民标识"}, "taxableInd": {"type": "string", "description": "收税标志"}, "taxpayerId": {"type": "string", "description": "纳税人识别号"}, "taxpayerType": {"type": "string", "description": "纳税人类型"}, "telCheckerFlag": {"type": "string", "description": "恐怖组织标志"}, "tempClient": {"type": "string", "description": "临时客户标志"}, "threeOrLayer": {"type": "string", "description": "三层或三层以上股权结构"}, "tranEmail": {"type": "string", "description": "交易用EMAIL"}, "tranTimestamp": {"type": "string", "description": "交易时间戳"}, "treatment": {"type": "string", "description": "处置方式"}, "trustInUnchain": {"type": "string", "description": "所有权链中的信托"}, "unprovideIdReason": {"type": "string", "description": "未提供识别号原因"}, "unresInvestFund": {"type": "string", "description": "不受监管的投资基金"}, "unverificationReason": {"type": "string", "description": "无法核实原因"}, "userDefineArray": {"type": "array", "description": "用户自定义数据表", "items": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "columnCode": {"type": "string", "description": "字段编码"}, "columnLength": {"type": "string", "description": "字段长度"}, "columnName": {"type": "string", "description": "列名"}, "columnType": {"type": "string", "description": "字段类型"}, "columnValue": {"type": "string", "description": "取值范围"}}}}, "verificationResult": {"type": "string", "description": "核查结果"}, "voucherType": {"type": "string", "description": "开票类型"}, "writtenLanguage": {"type": "string", "description": "书写语言"}, "wrnFlag": {"type": "string", "description": "贷款核销标志"}, "ydtFlag": {"type": "string", "description": "客户级易贷通标志"}, "yearlyIncome": {"type": "number", "description": "年收入", "format": "double"}}}, "Response客户首选信息查询": {"type": "object", "properties": {"addressId": {"type": "string", "description": "地址编号"}, "adviceRequiredCode": {"type": "string", "description": "通知代码"}, "birthDate": {"type": "string", "description": "出生日期"}, "blacklistIndFlag": {"type": "string", "description": "是否黑名单客户"}, "branchInnerFlag": {"type": "string", "description": "是否机构内客户标志"}, "categoryType": {"type": "string", "description": "客户细分类型"}, "centralBankCustomerClass": {"type": "string", "description": "央行客户分类"}, "centralBankCustomerGroup": {"type": "string", "description": "央行客户分组"}, "childrenFlag": {"type": "string", "description": "未成年标志"}, "clientCity": {"type": "string", "description": "城市代码"}, "clientClass": {"type": "string", "description": "客户类别"}, "clientIndicator": {"type": "string", "description": "客户标识"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientShort": {"type": "string", "description": "客户简称"}, "clientTranStatus": {"type": "string", "description": "客户交易状态"}, "clientType": {"type": "string", "description": "客户类型"}, "company": {"type": "string", "description": "法人"}, "contactTel": {"type": "string", "description": "联系电话  "}, "contactTelUpdateDate": {"type": "string", "description": "联系电话更新日期"}, "corporation": {"type": "string", "description": "是否为企业"}, "countryLoc": {"type": "string", "description": "国籍"}, "crRating": {"type": "string", "description": "客户信用等级"}, "ctrlBranch": {"type": "string", "description": "控制分行"}, "district": {"type": "string", "description": "区号"}, "documentExpiryDate": {"type": "string", "description": "证件失效日期"}, "documentId": {"type": "string", "description": "证件号码"}, "documentIssDate": {"type": "string", "description": "证件签发日期"}, "documentType": {"type": "string", "description": "证件类型"}, "education": {"type": "string", "description": "教育程度编号"}, "enClientName": {"type": "string", "description": "客户英文名称"}, "enClientShort": {"type": "string", "description": "客户英文简称"}, "ghoCustomerClass": {"type": "string", "description": "集团客户分类"}, "ghoCustomerGroup": {"type": "string", "description": "集团客户分组"}, "gimisAttribute1": {"type": "string", "description": "GIMIS属性1"}, "gimisAttribute2": {"type": "string", "description": "GIMIS属性2"}, "gimisCustomerIndicator": {"type": "string", "description": "GIMIS客户标识"}, "industry": {"type": "string", "description": "通用行业代码"}, "industryLevel": {"type": "string", "description": "通用行业层级"}, "infoLack": {"type": "string", "description": "资料不全标志"}, "inlandOffshore": {"type": "string", "description": "境内境外标志"}, "internalIndFlag": {"type": "string", "description": "内部客户标志"}, "isIndividual": {"type": "string", "description": "个体客户标志"}, "issCountry": {"type": "string", "description": "发证国家"}, "marketSector1": {"type": "string", "description": "市场部门1"}, "marketSector2": {"type": "string", "description": "市场部门2"}, "marketSector3": {"type": "string", "description": "市场部门3"}, "marketSectorProportion1": {"type": "string", "description": "市场部门1比例"}, "marketSectorProportion2": {"type": "string", "description": "市场部门2比例"}, "marketSectorProportion3": {"type": "string", "description": "市场部门3比例"}, "mobilePhone": {"type": "string", "description": "移动电话"}, "mobilePhone2": {"type": "string", "description": "移动电话2"}, "nation": {"type": "string", "description": "民族"}, "occupationCode": {"type": "string", "description": "职业"}, "printLanguage": {"type": "string", "description": "打印语言"}, "race": {"type": "string", "description": "种族"}, "relationArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"clientA": {"type": "string", "description": "客户A"}, "clientB": {"type": "string", "description": "客户B"}, "relationStatus": {"type": "string", "description": "关系状态"}, "relationType": {"type": "string", "description": "关系类型"}}}}, "relative": {"type": "string", "description": "是否亲戚"}, "resFlag": {"type": "string", "description": "冻结标志"}, "restraintArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"branch": {"type": "string", "description": "所属机构号"}, "company": {"type": "string", "description": "法人"}, "endDate": {"type": "string", "description": "结束日期"}, "lastChangeDate": {"type": "string", "description": "最后修改日期"}, "narrative": {"type": "string", "description": "摘要"}, "resSeqNo": {"type": "string", "description": "限制编号"}, "restraintType": {"type": "string", "description": "限制类型"}, "startDate": {"type": "string", "description": "开始日期"}, "status": {"type": "string", "description": "状态"}, "term": {"type": "string", "description": "存期期限"}, "termType": {"type": "string", "description": "期限类型"}}}}, "spokenLanguage": {"type": "string", "description": "交流语言"}, "state": {"type": "string", "description": "省别代码"}, "taxFlag": {"type": "string", "description": "是否税信息"}, "tempClient": {"type": "string", "description": "临时客户标志"}, "verificationResult": {"type": "string", "description": "核查结果"}, "visaType": {"type": "string", "description": "签证类型"}, "writtenLanguage": {"type": "string", "description": "书写语言"}, "wrnFlag": {"type": "string", "description": "贷款核销标志"}}}, "Response对公客户信息维护": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}, "Response对公客户信息维护(修改版)": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}, "Response对私客户信息维护": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}, "Response快速建立客户信息": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}, "Response批量导入查询": {"type": "object", "properties": {"listArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"auditTimestamp": {"type": "string", "description": "审计时间"}, "contactTel": {"type": "string", "description": "联系电话  "}, "dataType": {"type": "string", "description": "数据类型"}, "dataValue": {"type": "string", "description": "数据值"}, "listCategory": {"type": "string", "description": "名单种类代码"}, "listOrg": {"type": "string", "description": "名单发送/审核机构"}, "listType": {"type": "string", "description": "名单类型代码"}, "listTypeRemark": {"type": "string", "description": "名单类型冗余项"}, "loadDate": {"type": "string", "description": "导入日期"}, "name": {"type": "string", "description": "名称"}, "narrative": {"type": "string", "description": "摘要"}, "orgCode": {"type": "string", "description": "银行/支付机构编号"}, "orgContactor": {"type": "string", "description": "发送机构联系人"}, "validTerm": {"type": "string", "description": "有效期"}}}}}}, "Response批量开立客户信息": {"type": "object", "properties": {"contrastBatNo": {"type": "string", "description": "他行批次号"}}}, "Response批量开立结果文件生成": {"type": "object", "properties": {"fileName": {"type": "string", "description": "文件名称"}}}, "Response批量查询客户和账户信息": {"type": "object", "properties": {"fileName": {"type": "string", "description": "文件名称"}, "filePath": {"type": "string", "description": "文件路径"}}}, "Response批量核查结果文件下载": {"type": "object", "properties": {"resultFileName": {"type": "string", "description": "结果文件名"}}}, "Response批量核查结果登记": {"type": "object", "properties": {"contrastBatNo": {"type": "string", "description": "他行批次号"}}}, "Response按客户经理查询客户信息": {"type": "object", "properties": {"clientArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}}}}, "clientTranStatus": {"type": "string", "description": "客户交易状态"}, "ctrlBranch": {"type": "string", "description": "控制分行"}}}, "Response机构撤并客户校验项查询": {"type": "object", "properties": {"clientArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}}}}}}, "Response柜员权限查询": {"type": "object", "properties": {"businessList": {"type": "string", "description": "行业代码组"}, "clientTypeList": {"type": "string", "description": "客户类型组"}, "occupationCodeList": {"type": "string", "description": "职业组"}, "userId": {"type": "string", "description": "交易柜员"}}}, "Response查询和校验个人客户手机号码重复": {"type": "object", "properties": {"clientArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"clientNo": {"type": "string", "description": "客户号"}, "clientShort": {"type": "string", "description": "客户简称"}}}}}}, "Response查询客户基本信息(多笔)": {"type": "object", "properties": {"clientArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"- contactInfoArray": {"type": "array", "description": "数组", "items": {"type": "object"}}, "- documentInfoArray": {"type": "array", "description": "数组", "items": {"type": "object"}}, "acctExec": {"type": "string", "description": "客户经理"}, "address": {"type": "string", "description": "地址"}, "basicAcctNo": {"type": "string", "description": "基本账号"}, "birthDate": {"type": "string", "description": "出生日期"}, "blacklistIndFlag": {"type": "string", "description": "是否黑名单客户"}, "categoryType": {"type": "string", "description": "客户细分类型"}, "clientAlias": {"type": "string", "description": "别名"}, "clientGroup": {"type": "string", "description": "客户群组\n"}, "clientIndicator": {"type": "string", "description": "客户标识"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "clientShort": {"type": "string", "description": "客户简称"}, "clientStatus": {"type": "string", "description": "客户状态"}, "clientTranStatus": {"type": "string", "description": "客户交易状态"}, "clientType": {"type": "string", "description": "客户类型"}, "countryCitizen": {"type": "string", "description": "居住国家"}, "countryLoc": {"type": "string", "description": "国籍"}, "ctrlBranch": {"type": "string", "description": "控制分行"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "enClientName": {"type": "string", "description": "客户英文名称"}, "inlandOffshore": {"type": "string", "description": "境内境外标志"}, "internalIndFlag": {"type": "string", "description": "内部客户标志"}, "isIndividual": {"type": "string", "description": "个体客户标志"}, "issCountry": {"type": "string", "description": "发证国家"}, "placeOfBirth": {"type": "string", "description": "出生国"}, "printLanguage": {"type": "string", "description": "打印语言"}, "sourceType": {"type": "string", "description": "渠道类型"}, "tempClient": {"type": "string", "description": "临时客户标志"}}}}}}, "Response查询用户自定义参数": {"type": "object", "properties": {"listArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"columnCode": {"type": "string", "description": "字段编码"}, "columnLength": {"type": "string", "description": "字段长度"}, "columnName": {"type": "string", "description": "列名"}, "columnType": {"type": "string", "description": "字段类型"}, "columnValue": {"type": "string", "description": "取值范围"}, "columnValueDesc": {"type": "string", "description": "取值范围描述"}}}}}}, "Response根据客户号查询客户冻结信息": {"type": "object", "properties": {"blockArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"blockAuthUserId": {"type": "string", "description": "冻结授权柜员"}, "blockDate": {"type": "string", "description": "冻结日期"}, "blockGroupFlag": {"type": "string", "description": "是否群冻结"}, "blockReason": {"type": "string", "description": "冻结原因"}, "blockUserId": {"type": "string", "description": "冻结柜员"}, "clientNo": {"type": "string", "description": "客户号"}, "seqNo": {"type": "string", "description": "序号"}, "unblockAuthUserId": {"type": "string", "description": "解冻授权柜员"}, "unblockDate": {"type": "string", "description": "解冻日期"}, "unblockUserId": {"type": "string", "description": "解冻柜员"}, "unfrozenFlag": {"type": "string", "description": "解冻标志"}}}}}}, "Response根据客户号查询客户级限制信息": {"type": "object", "properties": {"restraintArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"authUserId": {"type": "string", "description": "授权柜员"}, "branch": {"type": "string", "description": "所属机构号"}, "chClientName": {"type": "string", "description": "客户中文名称"}, "company": {"type": "string", "description": "法人"}, "endDate": {"type": "string", "description": "结束日期"}, "lastChangeDate": {"type": "string", "description": "最后修改日期"}, "lastChangeUserId": {"type": "string", "description": "最后修改柜员"}, "narrative": {"type": "string", "description": "摘要"}, "resSeqNo": {"type": "string", "description": "限制编号"}, "restraintClass": {"type": "string", "description": "限制类型类别"}, "restraintType": {"type": "string", "description": "限制类型"}, "restraintsDesc": {"type": "string", "description": "限制中文描述信息"}, "startDate": {"type": "string", "description": "开始日期"}, "status": {"type": "string", "description": "状态"}, "term": {"type": "string", "description": "存期期限"}, "termType": {"type": "string", "description": "期限类型"}, "userId": {"type": "string", "description": "交易柜员"}}}}}}, "Response根据客户号查询客户联系人信息": {"type": "object", "properties": {"cifContactArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "linkmanName": {"type": "string", "description": "联系人名称"}, "linkmanType": {"type": "string", "description": "联系人类型"}, "phoneNo1": {"type": "string", "description": "电话号码 1"}, "phoneNo2": {"type": "string", "description": "联系人电话2"}}}}}}, "Response根据客户类型查询产品类型": {"type": "object", "properties": {"prodArray": {"type": "array", "description": "产品列表", "items": {"type": "object", "properties": {"prodDesc": {"type": "string", "description": "产品描述"}, "prodType": {"type": "string", "description": "产品类型"}}}}}}, "Response根据客户类型查询对公对私标志": {"type": "object", "properties": {"clientType": {"type": "string", "description": "客户类型"}, "clientTypeDesc": {"type": "string", "description": "客户类型描述"}, "company": {"type": "string", "description": "法人"}, "isIndividual": {"type": "string", "description": "个体客户标志"}}}, "Response相似客户查询": {"type": "object", "properties": {"clientArray": {"type": "array", "description": "数组", "items": {"type": "object", "properties": {"analagueRuleName": {"type": "string", "description": "相似规则名称"}, "analogueGroup": {"type": "string", "description": "相似组"}, "analogueRuleId": {"type": "string", "description": "相似规则编号"}, "branch": {"type": "string", "description": "所属机构号"}, "clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "groupBranch": {"type": "string", "description": "相似组有权处理机构"}, "issDate": {"type": "string", "description": "签发日期"}, "issueBranch": {"type": "string", "description": "签发机构"}, "maturityDate": {"type": "string", "description": "到期日期"}}}}}}, "Response联名客户关系建立": {"type": "object", "properties": {"clientName": {"type": "string", "description": "客户名称"}, "clientNo": {"type": "string", "description": "客户号"}, "jointNo": {"type": "string", "description": "联名编号"}, "virtualClientNo": {"type": "string", "description": "虚拟客户号"}}}, "Response联名客户查询": {"type": "object", "properties": {"jointClientArray": {"type": "array", "description": "联名客户数组", "items": {"type": "object", "properties": {"clientName": {"type": "string", "description": "客户名称"}, "documentId": {"type": "string", "description": "证件号码"}, "documentType": {"type": "string", "description": "证件类型"}, "issCountry": {"type": "string", "description": "发证国家"}, "jointClientNo": {"type": "string", "description": "联名客户号\n"}, "jointNo": {"type": "string", "description": "联名编号"}, "jointType": {"type": "string", "description": "联名类型"}, "mainClientFlag": {"type": "string", "description": "主客户标识"}, "virtualClientNo": {"type": "string", "description": "虚拟客户号"}}}}}}}}