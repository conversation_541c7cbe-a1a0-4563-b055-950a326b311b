# Excel to Swagger Converter

这是一个用Go语言编写的程序，用于解析Excel文件并生成Swagger API文档。

## 功能特性

- 解析Excel文件的第一个sheet作为API列表清单
- 根据API名称自动查找对应的sheet（包含出入参信息）
- 自动识别输入参数和输出参数
- 生成符合Swagger 2.0规范的JSON文件
- 支持嵌套对象和数组类型
- 自动映射数据类型和格式

## 安装依赖

```bash
go mod tidy
```

## 使用方法

```bash
go run main.go <excel_file_path>
```

例如：
```bash
go run main.go cif_test.xlsx
```

## Excel文件格式要求

### 第一个Sheet（API列表清单）
必须包含以下列：
- A: 序号
- B: 接口名称
- C: 接口定义
- D: 定义（API路径）
- E: 功能描述
- F: 业务分类
- G: 功能分类
- H: 消费系统
- I: 备注
- J: 使用状态
- K: 接口类名

### API详情Sheet
每个API名称对应一个同名的sheet，包含：
- 输入参数（In）：从"In"行开始到"Out"行结束
- 输出参数（Out）：从"Out"行开始到结束

每个参数包含：
- 字段名称、类型、中文名、是否必填等详细信息

## 输出

程序会生成一个JSON格式的Swagger文件，文件名为：`{原文件名}_swagger.json`

## 注意事项

1. Excel文件必须包含至少一个API列表sheet
2. API名称必须与对应的详情sheet名称完全匹配
3. 程序会自动识别参数类型并转换为相应的Swagger类型
4. 支持嵌套对象和数组类型的输出参数 