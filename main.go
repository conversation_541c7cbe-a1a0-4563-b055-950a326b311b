package main

import (
	"encoding/json"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"

	"github.com/xuri/excelize/v2"
)

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// APIDefinition 表示API定义
type APIDefinition struct {
	SerialNumber       string `json:"serialNumber"`
	InterfaceName      string `json:"interfaceName"`
	InterfaceDef       string `json:"interfaceDef"`
	Definition         string `json:"definition"`
	FunctionDesc       string `json:"functionDesc"`
	BusinessCategory   string `json:"businessCategory"`
	FunctionCategory   string `json:"functionCategory"`
	ConsumerSystem     string `json:"consumerSystem"`
	Remarks            string `json:"remarks"`
	UsageStatus        string `json:"usageStatus"`
	InterfaceClassName string `json:"interfaceClassName"`
}

// Parameter 表示参数定义
type Parameter struct {
	SerialNumber          string `json:"serialNumber"`
	FieldName             string `json:"fieldName"`
	FieldType             string `json:"fieldType"`
	FieldChineseName      string `json:"fieldChineseName"`
	IsRequired            string `json:"isRequired"`
	ValueRange            string `json:"valueRange"`
	ValueRangeDesc        string `json:"valueRangeDesc"`
	BusinessDesc          string `json:"businessDesc"`
	IsDataDictDefined     string `json:"isDataDictDefined"`
	ConstraintConditions  string `json:"constraintConditions"`
	RegexPattern          string `json:"regexPattern"`
	NumericRange          string `json:"numericRange"`
	ArrayUniqueCheck      string `json:"arrayUniqueCheck"`
	UniquenessField       string `json:"uniquenessField"`
	EnableParamTableCheck string `json:"enableParamTableCheck"`
	EnableParamTableCache string `json:"enableParamTableCache"`
	ParamTableName        string `json:"paramTableName"`
	ParamValueColumn      string `json:"paramValueColumn"`
	ErrorMessageCode      string `json:"errorMessageCode"`
	WhereCondition        string `json:"whereCondition"`
	MaxLength             string `json:"maxLength"`
	MinLength             string `json:"minLength"`
	DateFormat            string `json:"dateFormat"`
	IsPureNumber          string `json:"isPureNumber"`
	NestedLevel           int    `json:"nestedLevel"` // 嵌套层级，0表示顶级，1表示第一级嵌套，以此类推
}

// APIDetail 表示API详细信息（包含出入参）
type APIDetail struct {
	InputParams  []Parameter `json:"inputParams"`
	OutputParams []Parameter `json:"outputParams"`
}

// SwaggerInfo 表示Swagger基本信息
type SwaggerInfo struct {
	Title       string `json:"title"`
	Description string `json:"description"`
	Version     string `json:"version"`
}

// SwaggerPath 表示Swagger路径
type SwaggerPath struct {
	Get    *SwaggerOperation `json:"get,omitempty"`
	Post   *SwaggerOperation `json:"post,omitempty"`
	Put    *SwaggerOperation `json:"put,omitempty"`
	Delete *SwaggerOperation `json:"delete,omitempty"`
}

// SwaggerOperation 表示Swagger操作
type SwaggerOperation struct {
	Tags        []string                   `json:"tags"`
	Summary     string                     `json:"summary"`
	Description string                     `json:"description"`
	OperationID string                     `json:"operationId"`
	Parameters  []SwaggerParameter         `json:"parameters"`
	Responses   map[string]SwaggerResponse `json:"responses"`
}

// SwaggerParameter 表示Swagger参数
type SwaggerParameter struct {
	Name        string         `json:"name"`
	In          string         `json:"in"`
	Description string         `json:"description"`
	Required    bool           `json:"required"`
	Type        string         `json:"type"`
	Format      string         `json:"format,omitempty"`
	Schema      *SwaggerSchema `json:"schema,omitempty"`
}

// SwaggerResponse 表示Swagger响应
type SwaggerResponse struct {
	Description string        `json:"description"`
	Schema      SwaggerSchema `json:"schema"`
}

// SwaggerSchema 表示Swagger模式
type SwaggerSchema struct {
	Type        string                   `json:"type,omitempty"`
	Description string                   `json:"description,omitempty"`
	Format      string                   `json:"format,omitempty"`
	Items       *SwaggerSchema           `json:"items,omitempty"`
	Properties  map[string]SwaggerSchema `json:"properties,omitempty"`
	Ref         string                   `json:"$ref,omitempty"`
}

// SwaggerDocument 表示完整的Swagger文档
type SwaggerDocument struct {
	Swagger     string                   `json:"swagger"`
	Info        SwaggerInfo              `json:"info"`
	Host        string                   `json:"host"`
	BasePath    string                   `json:"basePath"`
	Schemes     []string                 `json:"schemes"`
	Paths       map[string]SwaggerPath   `json:"paths"`
	Definitions map[string]SwaggerSchema `json:"definitions"`
}

func main() {
	// if len(os.Args) != 2 {
	// 	fmt.Println("Usage: go run main.go <excel_file_path>")
	// 	os.Exit(1)
	// }

	// excelPath := "os.Args[1]"cif_test.xlsx
	excelPath := "cif_test.xlsx"

	// 打开Excel文件
	f, err := excelize.OpenFile(excelPath)
	if err != nil {
		log.Fatalf("Failed to open Excel file: %v", err)
	}
	defer f.Close()

	// 获取所有sheet名称
	sheets := f.GetSheetList()
	if len(sheets) == 0 {
		log.Fatal("No sheets found in Excel file")
	}

	// 读取第一个sheet（API列表清单）
	apiListSheet := sheets[1]
	fmt.Printf("Reading API list from sheet: %s\n", apiListSheet)

	apiDefinitions, err := readAPIList(f, apiListSheet)
	if err != nil {
		log.Fatalf("Failed to read API list: %v", err)
	}

	fmt.Printf("Found %d API definitions\n", len(apiDefinitions))

	// 为每个API读取详细信息
	apiDetails := make(map[string]APIDetail)
	fmt.Printf("Available sheets: %v\n", sheets)

	for _, api := range apiDefinitions {
		fmt.Printf("\nProcessing API: %s\n", api.InterfaceName)

		if api.InterfaceName == "根据客户类型查询产品类型" {
			fmt.Printf("\nProcessing API: %s\n", api.InterfaceName)
		}

		// 查找同名的sheet
		if sheetName := findSheetByName(sheets, api.InterfaceName); sheetName != "" {
			fmt.Printf("Found detail sheet: %s\n", sheetName)

			detail, err := readAPIDetail(f, sheetName)
			if err != nil {
				fmt.Printf("Warning: Failed to read details for API '%s': %v\n", api.InterfaceName, err)
				continue
			}

			fmt.Printf("Successfully read details - Input params: %d, Output params: %d\n",
				len(detail.InputParams), len(detail.OutputParams))

			apiDetails[api.InterfaceName] = detail
		} else {
			fmt.Printf("Warning: No detail sheet found for API '%s'\n", api.InterfaceName)
			// 尝试模糊匹配
			for _, sheet := range sheets {
				if strings.Contains(strings.ToLower(sheet), strings.ToLower(api.InterfaceName)) ||
					strings.Contains(strings.ToLower(api.InterfaceName), strings.ToLower(sheet)) {
					fmt.Printf("Possible match found: %s\n", sheet)
				}
			}
		}
	}

	// 生成Swagger文档
	swaggerDoc := generateSwagger(apiDefinitions, apiDetails)

	// 输出Swagger文件
	outputPath := strings.TrimSuffix(filepath.Base(excelPath), filepath.Ext(excelPath)) + "_swagger.json"
	err = writeSwaggerFile(swaggerDoc, outputPath)
	if err != nil {
		log.Fatalf("Failed to write Swagger file: %v", err)
	}

	fmt.Printf("Swagger file generated successfully: %s\n", outputPath)
}

// readAPIList 读取API列表
func readAPIList(f *excelize.File, sheetName string) ([]APIDefinition, error) {
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return nil, err
	}

	if len(rows) < 3 {
		return nil, fmt.Errorf("insufficient rows in sheet %s", sheetName)
	}

	var apis []APIDefinition

	// 从第3行开始读取数据（前两行是标题和说明）
	for i := 2; i < len(rows); i++ {
		row := rows[i]
		if len(row) < 11 {
			continue
		}

		// 跳过空行
		if row[1] == "" {
			continue
		}

		api := APIDefinition{
			SerialNumber:       getCellValue(row, 0),
			InterfaceName:      getCellValue(row, 1),
			InterfaceDef:       getCellValue(row, 2),
			Definition:         getCellValue(row, 3),
			FunctionDesc:       getCellValue(row, 4),
			BusinessCategory:   getCellValue(row, 5),
			FunctionCategory:   getCellValue(row, 6),
			ConsumerSystem:     getCellValue(row, 7),
			Remarks:            getCellValue(row, 8),
			UsageStatus:        getCellValue(row, 9),
			InterfaceClassName: getCellValue(row, 10),
		}

		apis = append(apis, api)
	}

	return apis, nil
}

// findSheetByName 根据API名称查找对应的sheet
func findSheetByName(sheets []string, apiName string) string {
	for _, sheet := range sheets {
		if strings.TrimSpace(sheet) == strings.TrimSpace(apiName) {
			return sheet
		}
	}
	return ""
}

// readAPIDetail 读取API详细信息（出入参）
func readAPIDetail(f *excelize.File, sheetName string) (APIDetail, error) {
	rows, err := f.GetRows(sheetName)
	if err != nil {
		return APIDetail{}, err
	}

	fmt.Printf("\n=== Reading API Detail for Sheet: %s ===\n", sheetName)
	fmt.Printf("Sheet '%s' has %d rows\n", sheetName, len(rows))

	if len(rows) < 6 {
		return APIDetail{}, fmt.Errorf("insufficient rows in sheet %s (need at least 6 rows)", sheetName)
	}

	// 打印前20行的内容来了解结构
	maxRows := 20
	if len(rows) < maxRows {
		maxRows = len(rows)
	}
	fmt.Printf("\n--- First %d rows content ---\n", maxRows)
	for i := 0; i < maxRows; i++ {
		if len(rows[i]) > 0 {
			fmt.Printf("Row %d: %v\n", i, rows[i])
		}
	}

	var inputParams, outputParams []Parameter

	// 查找输入参数和输出参数的分界
	inputStart := -1
	outputStart := -1

	// 查找 "In" 和 "Out" 标记，从第5行开始查找
	fmt.Printf("\n--- Searching for In/Out markers starting from row 5 ---\n")
	for i := 4; i < len(rows); i++ { // 从第5行开始（索引4）
		if len(rows[i]) > 0 {
			// 尝试多个列来查找In/Out标记
			var cellValue string
			for j := 0; j < len(rows[i]) && j < 3; j++ { // 检查前3列
				cellValue = strings.TrimSpace(rows[i][j])
				if cellValue == "In" || cellValue == "Out" {
					break
				}
			}

			fmt.Printf("Row %d: checking cell values: %v, found: '%s'\n", i, rows[i][:min(3, len(rows[i]))], cellValue)

			if cellValue == "In" {
				inputStart = i + 1 // 输入参数从"In"的下一行开始
				fmt.Printf("Found 'In' at row %d, input parameters start at row %d\n", i, inputStart)
			} else if cellValue == "Out" {
				outputStart = i + 1 // 输出参数从"Out"的下一行开始
				fmt.Printf("Found 'Out' at row %d, output parameters start at row %d\n", i, outputStart)
				break
			}
		}
	}

	fmt.Printf("\nFinal - Input start: %d, Output start: %d\n", inputStart, outputStart)

	// 读取输入参数
	if inputStart != -1 && outputStart != -1 && inputStart < outputStart {
		fmt.Printf("\n--- Reading Input Parameters (rows %d to %d) ---\n", inputStart, outputStart-1)
		inputParams = readParameters(rows[inputStart:outputStart])
		fmt.Printf("Read %d input parameters\n", len(inputParams))
	} else if inputStart != -1 && outputStart == -1 {
		// 只有输入参数
		fmt.Printf("\n--- Reading Input Parameters (rows %d to end) ---\n", inputStart)
		inputParams = readParameters(rows[inputStart:])
		fmt.Printf("Read %d input parameters (no output section found)\n", len(inputParams))
	}

	// 读取输出参数
	if outputStart != -1 {
		fmt.Printf("\n--- Reading Output Parameters (rows %d to end) ---\n", outputStart)
		outputParams = readParameters(rows[outputStart:])
		fmt.Printf("Read %d output parameters\n", len(outputParams))
	}

	// 打印读取到的参数详情
	fmt.Printf("\n--- Parameter Summary ---\n")
	fmt.Printf("Input Parameters: %d\n", len(inputParams))
	for i, param := range inputParams {
		fmt.Printf("  %d. %s (%s) - %s - Required: %s\n",
			i+1, param.FieldName, param.FieldType, param.FieldChineseName, param.IsRequired)
	}

	fmt.Printf("Output Parameters: %d\n", len(outputParams))
	for i, param := range outputParams {
		fmt.Printf("  %d. %s (%s) - %s - Required: %s\n",
			i+1, param.FieldName, param.FieldType, param.FieldChineseName, param.IsRequired)
	}

	return APIDetail{
		InputParams:  inputParams,
		OutputParams: outputParams,
	}, nil
}

// readParameters 读取参数列表，包括嵌套结构
func readParameters(rows [][]string) []Parameter {
	var params []Parameter

	fmt.Printf("Processing %d rows for parameters\n", len(rows))

	for i, row := range rows {
		// 打印每行的内容用于调试
		if i < 5 { // 只打印前5行用于调试
			fmt.Printf("  Row %d: %v\n", i, row)
		}

		// 检查行是否为空
		if len(row) == 0 {
			continue
		}

		// 检查是否有足够的列（至少需要6列：空列、序号、字段名、字段类型、中文名、是否必输）
		if len(row) < 6 {
			fmt.Printf("  Row %d: Insufficient columns (%d < 6), skipping\n", i, len(row))
			continue
		}

		// 跳过空行（检查关键字段）
		if getCellValue(row, 2) == "" { // 字段名称在第3列（索引2）
			fmt.Printf("  Row %d: Empty field name, skipping\n", i)
			continue
		}

		// 跳过表头行（包含"序号"、"字段名称"等）
		if getCellValue(row, 1) == "序号" || getCellValue(row, 2) == "字段名称" {
			fmt.Printf("  Row %d: Header row, skipping\n", i)
			continue
		}

		// 获取序号，用于判断嵌套层级
		serialNumber := getCellValue(row, 1) // 序号在第2列（索引1）

		// 处理字段名称：去掉"- "前缀
		fieldName := getCellValue(row, 2) // 字段名称在第3列（索引2）

		// 去掉"- "前缀（如果存在）
		fieldName = strings.TrimPrefix(fieldName, "- ")

		// 去掉前导和尾随空格，得到干净的字段名
		cleanFieldName := strings.TrimSpace(fieldName)

		// 根据Excel的实际结构调整列索引映射（第一列是空的，跳过）
		param := Parameter{
			SerialNumber:          serialNumber,          // 序号（第2列）
			FieldName:             cleanFieldName,        // 处理后的字段名称
			FieldType:             getCellValue(row, 3),  // 字段类型（第4列）
			FieldChineseName:      getCellValue(row, 4),  // 字段中文名（第5列）
			IsRequired:            getCellValue(row, 5),  // 是否必输（第6列）
			ValueRange:            getCellValue(row, 6),  // 取值范围（第7列）
			ValueRangeDesc:        getCellValue(row, 7),  // 取值范围释义（第8列）
			BusinessDesc:          getCellValue(row, 8),  // 业务描述（第9列）
			IsDataDictDefined:     getCellValue(row, 9),  // 是否定义数据字典（第10列）
			ConstraintConditions:  getCellValue(row, 10), // 约束条件（第11列）
			RegexPattern:          getCellValue(row, 11), // 正则表达式（第12列）
			NumericRange:          getCellValue(row, 12), // 数值区间（第13列）
			ArrayUniqueCheck:      getCellValue(row, 13), // 数组唯一检查（第14列）
			UniquenessField:       getCellValue(row, 14), // 唯一性字段（第15列）
			EnableParamTableCheck: getCellValue(row, 15), // 是否开启参数表校验（第16列）
			EnableParamTableCache: getCellValue(row, 16), // 是否开启参数表数据缓存（第17列）
			ParamTableName:        getCellValue(row, 17), // 参数表-表名（第18列）
			ParamValueColumn:      getCellValue(row, 18), // 参数值-列名（第19列）
			ErrorMessageCode:      getCellValue(row, 19), // 异常消息码/消息内容（第20列）
			WhereCondition:        getCellValue(row, 20), // where条件（第21列）
			MaxLength:             getCellValue(row, 21), // 最大长度（第22列）
			MinLength:             getCellValue(row, 22), // 最小长度（第23列）
			DateFormat:            getCellValue(row, 23), // 日期格式（第24列）
			IsPureNumber:          getCellValue(row, 24), // 是否纯数字（第25列）
		}

		// 验证参数的有效性
		if param.FieldName != "" && param.FieldType != "" {
			// 根据序号计算嵌套层级信息
			param.NestedLevel = calculateNestedLevel(serialNumber)

			params = append(params, param)
			if i < 5 { // 只打印前5个参数的详情
				fmt.Printf("  Added param: %s (%s) - %s [Level: %d, Serial: %s]\n",
					param.FieldName, param.FieldType, param.FieldChineseName, param.NestedLevel, param.SerialNumber)
			}
		} else {
			fmt.Printf("  Row %d: Invalid parameter (empty name or type), skipping\n", i)
		}
	}

	fmt.Printf("Successfully processed %d valid parameters\n", len(params))
	return params
}

// calculateNestedLevel 根据序号计算嵌套层级
// 例如：1 -> 0, 1.1 -> 1, 1.25.1 -> 2
func calculateNestedLevel(serialNumber string) int {
	if serialNumber == "" {
		return 0
	}

	// 计算点号的数量，点号数量就是嵌套层级
	dotCount := strings.Count(serialNumber, ".")
	return dotCount
}

// getCellValue 安全获取单元格值
func getCellValue(row []string, index int) string {
	if index < len(row) {
		// return strings.TrimSpace(row[index])
		return row[index]
	}
	return ""
}

// generateSwagger 生成Swagger文档
func generateSwagger(apis []APIDefinition, apiDetails map[string]APIDetail) SwaggerDocument {
	swagger := SwaggerDocument{
		Swagger: "2.0",
		Info: SwaggerInfo{
			Title:       "API Documentation",
			Description: "Generated from Excel specification",
			Version:     "1.0.0",
		},
		Host:        "localhost:8080",
		BasePath:    "/",
		Schemes:     []string{"http", "https"},
		Paths:       make(map[string]SwaggerPath),
		Definitions: make(map[string]SwaggerSchema),
	}

	// 为每个API生成路径
	for _, api := range apis {
		if api.Definition == "" {
			continue
		}

		if api.InterfaceName == "根据客户类型查询产品类型" {
			fmt.Printf("\nProcessing API: %s\n", api.InterfaceName)
		}

		path := SwaggerPath{}
		operation := &SwaggerOperation{
			// Tags:        []string{api.BusinessCategory},
			Summary:     api.InterfaceName,
			Description: api.FunctionDesc,
			OperationID: api.InterfaceClassName,
			Parameters:  []SwaggerParameter{},
			Responses:   make(map[string]SwaggerResponse),
		}

		// 添加输入参数（request body）
		if detail, exists := apiDetails[api.InterfaceName]; exists && len(detail.InputParams) > 0 {
			// 创建request body schema
			requestSchema := generateRequestSchema(detail.InputParams)

			// 添加request body参数
			requestBodyParam := SwaggerParameter{
				Name:        "body",
				In:          "body",
				Description: "Request body",
				Required:    true,
				Schema:      &requestSchema,
			}
			operation.Parameters = append(operation.Parameters, requestBodyParam)
		}

		// 添加响应
		if detail, exists := apiDetails[api.InterfaceName]; exists {
			responseSchema := generateResponseSchema(detail.OutputParams)
			operation.Responses["200"] = SwaggerResponse{
				Description: "Success",
				Schema:      responseSchema,
			}
		}

		// 所有API都使用POST方法
		path.Post = operation

		swagger.Paths[api.Definition] = path
	}

	// 生成定义
	generateDefinitions(swagger, apiDetails)

	return swagger
}

// generateRequestSchema 生成请求模式
func generateRequestSchema(inputParams []Parameter) SwaggerSchema {
	if len(inputParams) == 0 {
		return SwaggerSchema{
			Type:       "object",
			Properties: map[string]SwaggerSchema{},
		}
	}

	// 生成对象请求
	return SwaggerSchema{
		Type:       "object",
		Properties: generatePropertiesFromParams(inputParams),
	}
}

// convertToSwaggerParameter 转换参数为Swagger格式
func convertToSwaggerParameter(param Parameter, paramIn string) SwaggerParameter {
	swaggerParam := SwaggerParameter{
		Name:        param.FieldName, // 使用实际字段名而不是序号
		In:          paramIn,
		Description: param.FieldChineseName,
		Required:    param.IsRequired == "Y",
		Type:        getSwaggerType(param.FieldType),
	}

	// 设置格式
	if format := getSwaggerFormat(param.FieldType); format != "" {
		swaggerParam.Format = format
	}

	// 设置最大/最小长度
	if param.MaxLength != "" {
		// 这里可以添加maxLength属性
	}

	return swaggerParam
}

// getSwaggerType 获取Swagger类型
func getSwaggerType(fieldType string) string {
	if strings.Contains(fieldType, "String") {
		return "string"
	} else if strings.Contains(fieldType, "Integer") || strings.Contains(fieldType, "Long") {
		return "integer"
	} else if strings.Contains(fieldType, "Decimal") || strings.Contains(fieldType, "Double") {
		return "number"
	} else if strings.Contains(fieldType, "Date") || strings.Contains(fieldType, "DateTime") {
		return "string"
	} else if strings.Contains(fieldType, "List") || strings.Contains(fieldType, "Array") {
		return "array"
	}
	return "string"
}

// getSwaggerFormat 获取Swagger格式
func getSwaggerFormat(fieldType string) string {
	if strings.Contains(fieldType, "Date") {
		return "date"
	} else if strings.Contains(fieldType, "DateTime") {
		return "date-time"
	} else if strings.Contains(fieldType, "Long") {
		return "int64"
	} else if strings.Contains(fieldType, "Integer") {
		return "int32"
	} else if strings.Contains(fieldType, "Decimal") || strings.Contains(fieldType, "Double") {
		return "double"
	}
	return ""
}

// generateResponseSchema 生成响应模式
func generateResponseSchema(outputParams []Parameter) SwaggerSchema {
	if len(outputParams) == 0 {
		return SwaggerSchema{
			Type:       "object",
			Properties: map[string]SwaggerSchema{},
		}
	}

	// 检查是否有数组类型的输出参数
	for _, param := range outputParams {
		if strings.Contains(param.FieldType, "List") || strings.Contains(param.FieldType, "Array") {
			// 生成数组响应
			// 对于嵌套类型如 List<ListArray>，需要创建嵌套的schema
			if strings.Contains(param.FieldType, "<") && strings.Contains(param.FieldType, ">") {
				// 提取内部类型名称，如 ListArray from List<ListArray>
				return SwaggerSchema{
					Type: "array",
					Items: &SwaggerSchema{
						Type:       "object",
						Properties: generatePropertiesForInnerType(outputParams, param.FieldName),
					},
				}
			} else {
				// 简单数组类型
				return SwaggerSchema{
					Type: "array",
					Items: &SwaggerSchema{
						Type:       "object",
						Properties: generatePropertiesFromParams(outputParams),
					},
				}
			}
		}
	}

	// 生成对象响应
	return SwaggerSchema{
		Type:       "object",
		Properties: generatePropertiesFromParams(outputParams),
	}
}

// extractInnerType 从复杂类型中提取内部类型名称
// 例如：从 List<ListArray> 中提取 ListArray
func extractInnerType(fieldType string) string {
	start := strings.Index(fieldType, "<")
	end := strings.LastIndex(fieldType, ">")
	if start != -1 && end != -1 && end > start {
		return fieldType[start+1 : end]
	}
	return fieldType
}

// generatePropertiesForInnerType 为嵌套类型生成属性，包含其子字段
func generatePropertiesForInnerType(allParams []Parameter, parentFieldName string) map[string]SwaggerSchema {
	return generatePropertiesForInnerTypeWithDepth(allParams, parentFieldName, 0)
}

// generatePropertiesForInnerTypeWithDepth 为嵌套类型生成属性，包含递归深度控制
func generatePropertiesForInnerTypeWithDepth(allParams []Parameter, parentFieldName string, depth int) map[string]SwaggerSchema {
	// 防止无限递归，限制最大深度为3
	if depth > 3 {
		return map[string]SwaggerSchema{}
	}

	properties := make(map[string]SwaggerSchema)

	// 查找属于该父字段的子参数（NestedLevel = 1 且属于该父字段）
	for _, param := range allParams {
		// 只处理第一级嵌套参数
		if param.NestedLevel != 1 {
			continue
		}

		// 检查该参数是否属于指定的父字段
		// 通过检查序号格式来判断（如1.1, 1.2等属于序号1的字段）
		if !isChildOfParentBySerialNumber(param.SerialNumber, parentFieldName, allParams) {
			continue
		}

		schema := SwaggerSchema{
			Type:        getSwaggerType(param.FieldType),
			Description: param.FieldChineseName,
		}

		// 设置格式
		if format := getSwaggerFormat(param.FieldType); format != "" {
			schema.Format = format
		}

		// 如果是List类型，需要特殊处理
		if strings.Contains(param.FieldType, "List") || strings.Contains(param.FieldType, "Array") {
			if strings.Contains(param.FieldType, "<") && strings.Contains(param.FieldType, ">") {
				// 嵌套List类型，如 List<ListArray>
				schema.Type = "array"
				innerTypeName := extractInnerType(param.FieldType)
				schema.Items = &SwaggerSchema{
					Type:       "object",
					Properties: generatePropertiesForInnerTypeWithDepth(allParams, innerTypeName, depth+1),
				}
			} else {
				// 简单List类型
				schema.Type = "array"
				schema.Items = &SwaggerSchema{
					Type: "string", // 默认类型
				}
			}
		}

		// 使用字段名作为属性名
		properties[param.FieldName] = schema
	}

	return properties
}

// isChildOfParentBySerialNumber 通过序号判断父子关系
func isChildOfParentBySerialNumber(serialNumber, parentFieldName string, allParams []Parameter) bool {
	if !strings.Contains(serialNumber, ".") {
		return false
	}

	parts := strings.Split(serialNumber, ".")
	if len(parts) < 2 {
		return false
	}

	// 提取父序号（如从1.1中提取1，从1.25.1中提取1.25）
	parentSerial := strings.Join(parts[:len(parts)-1], ".")

	// 查找对应的父字段（通过序号匹配）
	for _, param := range allParams {
		if param.SerialNumber == parentSerial {
			// 检查父字段的类型，如果是List<类型>，需要提取内部类型名
			if strings.Contains(param.FieldType, "List<") && strings.Contains(param.FieldType, ">") {
				innerTypeName := extractInnerType(param.FieldType)
				return innerTypeName == parentFieldName
			}
			// 对于普通字段，直接比较字段名（去掉"- "前缀）
			cleanParentFieldName := strings.TrimPrefix(param.FieldName, "- ")
			cleanParentFieldName = strings.TrimSpace(cleanParentFieldName)
			return cleanParentFieldName == parentFieldName
		}
	}

	return false
}

// generatePropertiesFromParams 从参数生成属性，支持嵌套结构
func generatePropertiesFromParams(params []Parameter) map[string]SwaggerSchema {
	properties := make(map[string]SwaggerSchema)

	for _, param := range params {
		// 只处理顶级参数（NestedLevel = 0）
		if param.NestedLevel != 0 {
			continue
		}

		schema := SwaggerSchema{
			Type:        getSwaggerType(param.FieldType),
			Description: param.FieldChineseName,
		}

		// 设置格式
		if format := getSwaggerFormat(param.FieldType); format != "" {
			schema.Format = format
		}

		// 如果是List类型或Object类型，需要特殊处理
		if strings.Contains(param.FieldType, "List") || strings.Contains(param.FieldType, "Array") {
			if strings.Contains(param.FieldType, "<") && strings.Contains(param.FieldType, ">") {
				// 嵌套List类型，如 List<ListArray>
				schema.Type = "array"
				// 查找该参数的子参数来构建对象结构
				childProperties := generateChildProperties(params, param.SerialNumber)
				schema.Items = &SwaggerSchema{
					Type:       "object",
					Properties: childProperties,
				}
			} else {
				// 简单List类型
				schema.Type = "array"
				schema.Items = &SwaggerSchema{
					Type: "string", // 默认类型
				}
			}
		} else if hasChildParameters(params, param.SerialNumber) {
			// 如果有子参数，说明这是一个对象类型
			schema.Type = "object"
			schema.Properties = generateChildProperties(params, param.SerialNumber)
		}

		// 使用实际字段名作为属性名
		properties[param.FieldName] = schema
	}

	return properties
}

// hasChildParameters 检查指定参数是否有子参数
func hasChildParameters(params []Parameter, parentSerialNumber string) bool {
	for _, param := range params {
		if isDirectChild(param.SerialNumber, parentSerialNumber) {
			return true
		}
	}
	return false
}

// generateChildProperties 生成子参数的属性
func generateChildProperties(params []Parameter, parentSerialNumber string) map[string]SwaggerSchema {
	properties := make(map[string]SwaggerSchema)

	for _, param := range params {
		if !isDirectChild(param.SerialNumber, parentSerialNumber) {
			continue
		}

		schema := SwaggerSchema{
			Type:        getSwaggerType(param.FieldType),
			Description: param.FieldChineseName,
		}

		// 设置格式
		if format := getSwaggerFormat(param.FieldType); format != "" {
			schema.Format = format
		}

		// 递归处理子参数的子参数
		if strings.Contains(param.FieldType, "List") || strings.Contains(param.FieldType, "Array") {
			if strings.Contains(param.FieldType, "<") && strings.Contains(param.FieldType, ">") {
				// 嵌套List类型
				schema.Type = "array"
				childProperties := generateChildProperties(params, param.SerialNumber)
				schema.Items = &SwaggerSchema{
					Type:       "object",
					Properties: childProperties,
				}
			} else {
				// 简单List类型
				schema.Type = "array"
				schema.Items = &SwaggerSchema{
					Type: "string",
				}
			}
		} else if hasChildParameters(params, param.SerialNumber) {
			// 如果有子参数，说明这是一个对象类型
			schema.Type = "object"
			schema.Properties = generateChildProperties(params, param.SerialNumber)
		}

		properties[param.FieldName] = schema
	}

	return properties
}

// isDirectChild 检查是否为直接子参数
// 例如：1.1 是 1 的直接子参数，1.25.1 是 1.25 的直接子参数
func isDirectChild(childSerial, parentSerial string) bool {
	if childSerial == "" || parentSerial == "" {
		return false
	}

	// 检查子序号是否以父序号开头，并且只多一级
	if !strings.HasPrefix(childSerial, parentSerial+".") {
		return false
	}

	// 获取子序号中父序号之后的部分
	suffix := strings.TrimPrefix(childSerial, parentSerial+".")

	// 如果后缀中没有点号，说明是直接子参数
	return !strings.Contains(suffix, ".")
}

// generateDefinitions 生成定义
func generateDefinitions(swagger SwaggerDocument, apiDetails map[string]APIDetail) {
	for apiName, detail := range apiDetails {
		// 为每个API生成定义
		if len(detail.OutputParams) > 0 {
			definitionName := "Response" + strings.ReplaceAll(apiName, " ", "")

			swagger.Definitions[definitionName] = SwaggerSchema{
				Type:       "object",
				Properties: generatePropertiesFromParams(detail.OutputParams),
			}
		}
	}
}

// writeSwaggerFile 写入Swagger文件
func writeSwaggerFile(swagger SwaggerDocument, outputPath string) error {
	data, err := json.MarshalIndent(swagger, "", "  ")
	if err != nil {
		return err
	}

	return os.WriteFile(outputPath, data, 0644)
}
